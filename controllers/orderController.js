const { Order, Service, User, Customer } = require('../models');
const { sequelize } = require('../config/db');
const { Op } = require('sequelize');

// @desc    创建新订单
// @route   POST /api/orders
// @access  Private
const createOrder = async (req, res) => {
  try {
    console.log('创建订单请求体:', req.body);

    const {
      customerId,
      serviceId,
      staffId,
      scheduledAt,
      address,
      price,
      status,
      paymentStatus,
      paymentMethod,
      notes,
    } = req.body;

    // 状态转换
    const statusMap = {
      'pending': '待确认',
      'confirmed': '已确认',
      'in_progress': '进行中',
      'completed': '已完成',
      'cancelled': '已取消'
    };

    const paymentStatusMap = {
      'unpaid': '未支付',
      'paid': '已支付',
      'refunded': '已退款'
    };

    // 尝试直接使用SQL创建订单，绕过外键约束
    try {
      const [result] = await sequelize.query(
        `INSERT INTO DOrder
         (customerName, customerPhone, customerAddress, scheduledDate, status, totalPrice, paymentStatus, createdAt, updatedAt)
         VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        {
          replacements: [
            '客户',
            '12345678901',
            address,
            scheduledAt,
            statusMap[status] || '待确认',
            price || 0,
            paymentStatusMap[paymentStatus] || '未支付'
          ],
          type: sequelize.QueryTypes.INSERT
        }
      );

      console.log('SQL插入结果:', result);

      // 获取插入的订单ID
      const orderId = result;

      // 构造响应对象
      const orderResponse = {
        id: orderId,
        customerName: '客户',
        customerPhone: '12345678901',
        customerAddress: address,
        scheduledDate: scheduledAt,
        status: statusMap[status] || '待确认',
        totalPrice: price || 0,
        paymentStatus: paymentStatusMap[paymentStatus] || '未支付',
        paymentMethod,
        notes,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      res.status(201).json(orderResponse);
    } catch (error) {
      console.error('SQL创建订单失败:', error);
      res.status(500);
      throw new Error(`创建订单失败: ${error.message}`);
    }
  } catch (error) {
    console.error('创建订单失败:', error);
    res.status(res.statusCode === 200 ? 500 : res.statusCode);
    res.json({
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? null : error.stack,
    });
  }
};

// @desc    获取所有订单
// @route   GET /api/orders
// @access  Private/Admin
const getOrders = async (req, res) => {
  try {
    console.log('查询参数:', req.query);

    // 构建查询条件
    const whereClause = {};

    // 处理搜索参数
    if (req.query.customerName) {
      whereClause.customerName = { [Op.like]: `%${req.query.customerName}%` };
    }

    if (req.query.customerPhone) {
      whereClause.customerPhone = { [Op.like]: `%${req.query.customerPhone}%` };
    }

    if (req.query.status) {
      whereClause.status = req.query.status;
    }

    if (req.query.paymentStatus) {
      whereClause.paymentStatus = req.query.paymentStatus;
    }

    // 处理日期范围
    if (req.query.startDate) {
      whereClause.scheduledDate = { ...whereClause.scheduledDate, [Op.gte]: req.query.startDate };
    }

    if (req.query.endDate) {
      whereClause.scheduledDate = { ...whereClause.scheduledDate, [Op.lte]: req.query.endDate };
    }

    console.log('查询条件:', whereClause);

    // 查询订单
    const orders = await Order.findAll({
      where: Object.keys(whereClause).length > 0 ? whereClause : undefined,
      include: [
        { model: Service, as: 'service' },
        { model: User, as: 'assignedStaff' }
      ]
    });

    res.json(orders);
  } catch (error) {
    console.error('获取订单列表失败:', error);
    res.status(500);
    res.json({
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? null : error.stack,
    });
  }
};

// @desc    获取我的订单
// @route   GET /api/orders/myorders
// @access  Private
const getMyOrders = async (req, res) => {
  try {
    const orders = await Order.findAll({
      where: { assignedStaffId: req.user.id },
      include: [
        { model: Service, as: 'service' },
        { model: User, as: 'assignedStaff' }
      ]
    });

    res.json(orders);
  } catch (error) {
    res.status(500);
    res.json({
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? null : error.stack,
    });
  }
};

// @desc    获取订单详情
// @route   GET /api/orders/:id
// @access  Private
const getOrderById = async (req, res) => {
  try {
    const order = await Order.findByPk(req.params.id, {
      include: [
        { model: Service, as: 'service' },
        { model: User, as: 'assignedStaff' }
      ]
    });

    if (order) {
      res.json(order);
    } else {
      res.status(404);
      throw new Error('订单不存在');
    }
  } catch (error) {
    res.status(res.statusCode === 200 ? 500 : res.statusCode);
    res.json({
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? null : error.stack,
    });
  }
};

// @desc    更新订单状态
// @route   PUT /api/orders/:id/status
// @access  Private
const updateOrderStatus = async (req, res) => {
  try {
    const { status } = req.body;

    const order = await Order.findByPk(req.params.id);

    if (order) {
      order.status = status || order.status;

      const updatedOrder = await order.save();

      // 获取包含关联数据的订单
      const fullOrder = await Order.findByPk(updatedOrder.id, {
        include: [
          { model: Service, as: 'service' },
          { model: User, as: 'assignedStaff' }
        ]
      });

      res.json(fullOrder);
    } else {
      res.status(404);
      throw new Error('订单不存在');
    }
  } catch (error) {
    res.status(res.statusCode === 200 ? 500 : res.statusCode);
    res.json({
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? null : error.stack,
    });
  }
};

// @desc    更新订单支付状态
// @route   PUT /api/orders/:id/pay
// @access  Private/Admin
const updateOrderPayment = async (req, res) => {
  try {
    const { paymentStatus } = req.body;

    const order = await Order.findByPk(req.params.id);

    if (order) {
      order.paymentStatus = paymentStatus || order.paymentStatus;

      const updatedOrder = await order.save();

      // 获取包含关联数据的订单
      const fullOrder = await Order.findByPk(updatedOrder.id, {
        include: [
          { model: Service, as: 'service' },
          { model: User, as: 'assignedStaff' }
        ]
      });

      res.json(fullOrder);
    } else {
      res.status(404);
      throw new Error('订单不存在');
    }
  } catch (error) {
    res.status(res.statusCode === 200 ? 500 : res.statusCode);
    res.json({
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? null : error.stack,
    });
  }
};

// @desc    分配员工到订单
// @route   PUT /api/orders/:id/assign
// @access  Private/Admin
const assignStaffToOrder = async (req, res) => {
  try {
    const { staffId } = req.body;

    const order = await Order.findByPk(req.params.id);

    if (order) {
      order.assignedStaffId = staffId;
      order.status = '已确认';

      const updatedOrder = await order.save();

      // 获取包含关联数据的订单
      const fullOrder = await Order.findByPk(updatedOrder.id, {
        include: [
          { model: Service, as: 'service' },
          { model: User, as: 'assignedStaff' }
        ]
      });

      res.json(fullOrder);
    } else {
      res.status(404);
      throw new Error('订单不存在');
    }
  } catch (error) {
    res.status(res.statusCode === 200 ? 500 : res.statusCode);
    res.json({
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? null : error.stack,
    });
  }
};

// @desc    删除订单
// @route   DELETE /api/orders/:id
// @access  Private/Admin
const deleteOrder = async (req, res) => {
  try {
    const order = await Order.findByPk(req.params.id);

    if (order) {
      await order.destroy();
      res.json({ message: '订单已删除' });
    } else {
      res.status(404);
      throw new Error('订单不存在');
    }
  } catch (error) {
    res.status(res.statusCode === 200 ? 500 : res.statusCode);
    res.json({
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? null : error.stack,
    });
  }
};

module.exports = {
  createOrder,
  getOrders,
  getMyOrders,
  getOrderById,
  updateOrderStatus,
  updateOrderPayment,
  assignStaffToOrder,
  deleteOrder,
};
