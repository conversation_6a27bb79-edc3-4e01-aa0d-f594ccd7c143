const asyncHandler = require('express-async-handler');
const { Review, Order, User, Customer } = require('../models');

// @desc    获取所有评价
// @route   GET /api/reviews
// @access  Private
const getReviews = asyncHandler(async (req, res) => {
  const reviews = await Review.findAll({
    include: [
      {
        model: Order,
        as: 'order',
        include: ['service']
      },
      {
        model: Customer,
        as: 'customer'
      },
      {
        model: User,
        as: 'staff',
        attributes: ['id', 'name', 'email', 'phone']
      }
    ],
    order: [['createdAt', 'DESC']]
  });
  res.json(reviews);
});

// @desc    获取单个评价
// @route   GET /api/reviews/:id
// @access  Private
const getReviewById = asyncHandler(async (req, res) => {
  const review = await Review.findByPk(req.params.id, {
    include: [
      {
        model: Order,
        as: 'order',
        include: ['service']
      },
      {
        model: Customer,
        as: 'customer'
      },
      {
        model: User,
        as: 'staff',
        attributes: ['id', 'name', 'email', 'phone']
      }
    ]
  });
  
  if (review) {
    res.json(review);
  } else {
    res.status(404);
    throw new Error('评价不存在');
  }
});

// @desc    创建评价
// @route   POST /api/reviews
// @access  Private
const createReview = asyncHandler(async (req, res) => {
  const { 
    orderId, 
    customerId, 
    staffId, 
    rating, 
    comment, 
    serviceQuality, 
    attitude, 
    punctuality 
  } = req.body;

  // 检查订单是否存在
  const order = await Order.findByPk(orderId);
  if (!order) {
    res.status(404);
    throw new Error('订单不存在');
  }

  // 检查客户是否存在
  const customer = await Customer.findByPk(customerId);
  if (!customer) {
    res.status(404);
    throw new Error('客户不存在');
  }

  // 检查员工是否存在
  const staff = await User.findByPk(staffId);
  if (!staff) {
    res.status(404);
    throw new Error('员工不存在');
  }

  // 检查是否已经评价过
  const existingReview = await Review.findOne({ where: { orderId } });
  if (existingReview) {
    res.status(400);
    throw new Error('该订单已经评价过了');
  }

  const review = await Review.create({
    orderId,
    customerId,
    staffId,
    rating,
    comment,
    serviceQuality,
    attitude,
    punctuality,
    status: 'published'
  });

  if (review) {
    res.status(201).json(review);
  } else {
    res.status(400);
    throw new Error('无效的评价数据');
  }
});

// @desc    更新评价
// @route   PUT /api/reviews/:id
// @access  Private
const updateReview = asyncHandler(async (req, res) => {
  const review = await Review.findByPk(req.params.id);
  
  if (review) {
    const { 
      rating, 
      comment, 
      serviceQuality, 
      attitude, 
      punctuality,
      status
    } = req.body;
    
    review.rating = rating || review.rating;
    review.comment = comment !== undefined ? comment : review.comment;
    review.serviceQuality = serviceQuality || review.serviceQuality;
    review.attitude = attitude || review.attitude;
    review.punctuality = punctuality || review.punctuality;
    review.status = status || review.status;
    
    const updatedReview = await review.save();
    res.json(updatedReview);
  } else {
    res.status(404);
    throw new Error('评价不存在');
  }
});

// @desc    删除评价
// @route   DELETE /api/reviews/:id
// @access  Private
const deleteReview = asyncHandler(async (req, res) => {
  const review = await Review.findByPk(req.params.id);
  
  if (review) {
    await review.destroy();
    res.json({ message: '评价已删除' });
  } else {
    res.status(404);
    throw new Error('评价不存在');
  }
});

// @desc    获取员工的所有评价
// @route   GET /api/reviews/staff/:id
// @access  Private
const getStaffReviews = asyncHandler(async (req, res) => {
  const staffId = req.params.id;
  
  // 检查员工是否存在
  const staff = await User.findByPk(staffId);
  if (!staff) {
    res.status(404);
    throw new Error('员工不存在');
  }
  
  const reviews = await Review.findAll({
    where: { staffId },
    include: [
      {
        model: Order,
        as: 'order',
        include: ['service']
      },
      {
        model: Customer,
        as: 'customer'
      }
    ],
    order: [['createdAt', 'DESC']]
  });
  
  res.json(reviews);
});

module.exports = {
  getReviews,
  getReviewById,
  createReview,
  updateReview,
  deleteReview,
  getStaffReviews
};
