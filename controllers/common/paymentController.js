/**
 * 支付控制器
 */
const paymentService = require('../../services/paymentService');
const { Order, Payment } = require('../../models');

/**
 * 创建微信支付订单
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.createWechatPayment = async (req, res) => {
  try {
    const { orderId, openid, tradeType } = req.body;
    
    if (!orderId) {
      return res.status(400).json({ message: '缺少参数orderId' });
    }
    
    // 查询订单
    const order = await Order.findByPk(orderId);
    
    if (!order) {
      return res.status(404).json({ message: '订单不存在' });
    }
    
    if (order.paymentStatus === 'paid') {
      return res.status(400).json({ message: '订单已支付' });
    }
    
    // 获取客户端IP
    const clientIp = req.headers['x-forwarded-for'] || 
                     req.connection.remoteAddress || 
                     req.socket.remoteAddress || 
                     req.connection.socket.remoteAddress;
    
    // 创建支付订单
    const paymentParams = await paymentService.createWechatPayment({
      orderId,
      totalAmount: order.totalAmount,
      description: `家政服务订单-${order.id}`,
      openid,
      tradeType: tradeType || 'JSAPI',
      clientIp,
    });
    
    res.json({
      message: '创建支付订单成功',
      paymentParams,
    });
  } catch (error) {
    console.error('创建微信支付订单失败:', error);
    res.status(500).json({ message: error.message || '创建微信支付订单失败' });
  }
};

/**
 * 处理微信支付通知
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.handleWechatPayNotify = async (req, res) => {
  try {
    // 获取通知数据
    let xmlData = '';
    req.on('data', chunk => {
      xmlData += chunk.toString();
    });
    
    req.on('end', async () => {
      try {
        // 处理支付通知
        const result = await paymentService.handleWechatPayNotify(xmlData);
        
        // 返回成功响应
        res.set('Content-Type', 'text/xml');
        res.send('<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>');
      } catch (error) {
        console.error('处理微信支付通知失败:', error);
        
        // 返回失败响应
        res.set('Content-Type', 'text/xml');
        res.send(`<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[${error.message}]]></return_msg></xml>`);
      }
    });
  } catch (error) {
    console.error('处理微信支付通知失败:', error);
    
    // 返回失败响应
    res.set('Content-Type', 'text/xml');
    res.send(`<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[${error.message}]]></return_msg></xml>`);
  }
};

/**
 * 查询支付订单状态
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.queryPaymentStatus = async (req, res) => {
  try {
    const { orderId } = req.params;
    
    if (!orderId) {
      return res.status(400).json({ message: '缺少参数orderId' });
    }
    
    // 查询支付记录
    const payment = await Payment.findOne({
      where: { orderId },
      order: [['createdAt', 'DESC']],
    });
    
    if (!payment) {
      return res.status(404).json({ message: '支付记录不存在' });
    }
    
    // 查询订单
    const order = await Order.findByPk(orderId);
    
    res.json({
      orderId,
      paymentStatus: payment.status,
      orderStatus: order ? order.status : null,
      paymentMethod: payment.paymentMethod,
      amount: payment.amount,
      createdAt: payment.createdAt,
      updatedAt: payment.updatedAt,
    });
  } catch (error) {
    console.error('查询支付订单状态失败:', error);
    res.status(500).json({ message: error.message || '查询支付订单状态失败' });
  }
};

/**
 * 关闭支付订单
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.closePayment = async (req, res) => {
  try {
    const { orderId } = req.params;
    
    if (!orderId) {
      return res.status(400).json({ message: '缺少参数orderId' });
    }
    
    // 查询支付记录
    const payment = await Payment.findOne({
      where: { orderId },
      order: [['createdAt', 'DESC']],
    });
    
    if (!payment) {
      return res.status(404).json({ message: '支付记录不存在' });
    }
    
    if (payment.status === 'completed') {
      return res.status(400).json({ message: '支付已完成，无法关闭' });
    }
    
    // 更新支付记录状态
    await payment.update({
      status: 'cancelled',
    });
    
    // 更新订单状态
    const order = await Order.findByPk(orderId);
    if (order) {
      await order.update({
        paymentStatus: 'cancelled',
      });
    }
    
    res.json({
      message: '关闭支付订单成功',
      orderId,
    });
  } catch (error) {
    console.error('关闭支付订单失败:', error);
    res.status(500).json({ message: error.message || '关闭支付订单失败' });
  }
};
