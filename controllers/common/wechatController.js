/**
 * 微信控制器
 */
const jwt = require('jsonwebtoken');
const wechatService = require('../../services/wechatService');
const { User } = require('../../models');

/**
 * 微信小程序登录
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.miniProgramLogin = async (req, res) => {
  try {
    const { code } = req.body;
    
    if (!code) {
      return res.status(400).json({ message: '缺少参数code' });
    }
    
    // 调用微信登录服务
    const loginResult = await wechatService.miniProgramLogin(code);
    const { user, openid, session_key } = loginResult;
    
    // 生成JWT令牌
    const token = jwt.sign(
      { id: user.id, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );
    
    // 返回用户信息和令牌
    res.json({
      id: user.id,
      name: user.name || '',
      avatar: user.avatar || '',
      role: user.role,
      token,
      openid,
    });
  } catch (error) {
    console.error('微信小程序登录失败:', error);
    res.status(500).json({ message: error.message || '微信小程序登录失败' });
  }
};

/**
 * 更新微信用户信息
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.updateUserInfo = async (req, res) => {
  try {
    const { openid, userInfo } = req.body;
    
    if (!openid || !userInfo) {
      return res.status(400).json({ message: '参数不完整' });
    }
    
    // 更新用户信息
    const user = await wechatService.updateUserInfo(openid, userInfo);
    
    res.json({
      id: user.id,
      name: user.name,
      avatar: user.avatar,
      role: user.role,
    });
  } catch (error) {
    console.error('更新微信用户信息失败:', error);
    res.status(500).json({ message: error.message || '更新微信用户信息失败' });
  }
};

/**
 * 微信公众号登录
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.officialAccountLogin = async (req, res) => {
  try {
    const { code } = req.body;
    
    if (!code) {
      return res.status(400).json({ message: '缺少参数code' });
    }
    
    // 调用微信公众号登录服务
    const loginResult = await wechatService.officialAccountLogin(code);
    const { user, openid } = loginResult;
    
    // 生成JWT令牌
    const token = jwt.sign(
      { id: user.id, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );
    
    // 返回用户信息和令牌
    res.json({
      id: user.id,
      name: user.name || '',
      avatar: user.avatar || '',
      role: user.role,
      token,
      openid,
    });
  } catch (error) {
    console.error('微信公众号登录失败:', error);
    res.status(500).json({ message: error.message || '微信公众号登录失败' });
  }
};

/**
 * 绑定微信账号
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.bindWechat = async (req, res) => {
  try {
    const { userId, openid, unionid } = req.body;
    
    if (!userId || !openid) {
      return res.status(400).json({ message: '参数不完整' });
    }
    
    // 查找用户
    const user = await User.findByPk(userId);
    
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }
    
    // 检查openid是否已被绑定
    const existingUser = await User.findOne({ where: { wechatOpenId: openid } });
    
    if (existingUser && existingUser.id !== userId) {
      return res.status(400).json({ message: '该微信账号已被其他用户绑定' });
    }
    
    // 更新用户信息
    await user.update({
      wechatOpenId: openid,
      wechatUnionId: unionid || null,
    });
    
    res.json({
      message: '微信账号绑定成功',
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
      },
    });
  } catch (error) {
    console.error('绑定微信账号失败:', error);
    res.status(500).json({ message: error.message || '绑定微信账号失败' });
  }
};

/**
 * 解绑微信账号
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.unbindWechat = async (req, res) => {
  try {
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({ message: '缺少参数userId' });
    }
    
    // 查找用户
    const user = await User.findByPk(userId);
    
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }
    
    // 更新用户信息
    await user.update({
      wechatOpenId: null,
      wechatUnionId: null,
    });
    
    res.json({
      message: '微信账号解绑成功',
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
      },
    });
  } catch (error) {
    console.error('解绑微信账号失败:', error);
    res.status(500).json({ message: error.message || '解绑微信账号失败' });
  }
};
