const asyncHandler = require('express-async-handler');
const { Customer, Order } = require('../models');
const { Op } = require('sequelize');

// @desc    获取所有客户
// @route   GET /api/customers
// @access  Private
const getCustomers = asyncHandler(async (req, res) => {
  const customers = await Customer.findAll({
    order: [['createdAt', 'DESC']]
  });
  res.json(customers);
});

// @desc    获取单个客户
// @route   GET /api/customers/:id
// @access  Private
const getCustomerById = asyncHandler(async (req, res) => {
  const customer = await Customer.findByPk(req.params.id);
  
  if (customer) {
    res.json(customer);
  } else {
    res.status(404);
    throw new Error('客户不存在');
  }
});

// @desc    创建客户
// @route   POST /api/customers
// @access  Private
const createCustomer = asyncHandler(async (req, res) => {
  const { name, phone, email, address, gender, birthdate, notes, source } = req.body;

  // 检查手机号是否已存在
  const customerExists = await Customer.findOne({ where: { phone } });
  if (customerExists) {
    res.status(400);
    throw new Error('该手机号已被注册');
  }

  const customer = await Customer.create({
    name,
    phone,
    email,
    address,
    gender,
    birthdate,
    notes,
    source,
    status: 'active'
  });

  if (customer) {
    res.status(201).json(customer);
  } else {
    res.status(400);
    throw new Error('无效的客户数据');
  }
});

// @desc    更新客户
// @route   PUT /api/customers/:id
// @access  Private
const updateCustomer = asyncHandler(async (req, res) => {
  const customer = await Customer.findByPk(req.params.id);
  
  if (customer) {
    const { name, phone, email, address, gender, birthdate, notes, source, status } = req.body;
    
    // 如果更新手机号，检查是否与其他客户重复
    if (phone && phone !== customer.phone) {
      const phoneExists = await Customer.findOne({ 
        where: { 
          phone,
          id: { [Op.ne]: req.params.id }
        } 
      });
      
      if (phoneExists) {
        res.status(400);
        throw new Error('该手机号已被其他客户使用');
      }
    }
    
    customer.name = name || customer.name;
    customer.phone = phone || customer.phone;
    customer.email = email || customer.email;
    customer.address = address || customer.address;
    customer.gender = gender || customer.gender;
    customer.birthdate = birthdate || customer.birthdate;
    customer.notes = notes !== undefined ? notes : customer.notes;
    customer.source = source || customer.source;
    customer.status = status || customer.status;
    
    const updatedCustomer = await customer.save();
    res.json(updatedCustomer);
  } else {
    res.status(404);
    throw new Error('客户不存在');
  }
});

// @desc    删除客户
// @route   DELETE /api/customers/:id
// @access  Private
const deleteCustomer = asyncHandler(async (req, res) => {
  const customer = await Customer.findByPk(req.params.id);
  
  if (customer) {
    // 检查客户是否有关联订单
    const hasOrders = await Order.findOne({ where: { customerId: req.params.id } });
    
    if (hasOrders) {
      // 如果有关联订单，将状态设为inactive而不是删除
      customer.status = 'inactive';
      await customer.save();
      res.json({ message: '客户已设为非活跃状态，因为存在关联订单' });
    } else {
      // 如果没有关联订单，可以直接删除
      await customer.destroy();
      res.json({ message: '客户已删除' });
    }
  } else {
    res.status(404);
    throw new Error('客户不存在');
  }
});

// @desc    获取客户订单历史
// @route   GET /api/customers/:id/orders
// @access  Private
const getCustomerOrders = asyncHandler(async (req, res) => {
  const customer = await Customer.findByPk(req.params.id);
  
  if (!customer) {
    res.status(404);
    throw new Error('客户不存在');
  }
  
  const orders = await Order.findAll({
    where: { customerId: req.params.id },
    order: [['createdAt', 'DESC']],
    include: ['service', 'assignedStaff']
  });
  
  res.json(orders);
});

module.exports = {
  getCustomers,
  getCustomerById,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomerOrders
};
