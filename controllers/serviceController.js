const { Service } = require('../models');
const { Op } = require('sequelize');

// @desc    获取所有服务
// @route   GET /api/services
// @access  Public
const getServices = async (req, res) => {
  try {
    console.log('查询参数:', req.query);

    // 构建查询条件
    const whereClause = {};

    // 处理搜索参数
    if (req.query.name) {
      whereClause.name = { [Op.like]: `%${req.query.name}%` };
    }

    if (req.query.category) {
      whereClause.category = req.query.category;
    }

    if (req.query.isActive !== undefined) {
      whereClause.isActive = req.query.isActive === 'true';
    }

    // 处理价格范围
    if (req.query.minPrice) {
      whereClause.price = { ...whereClause.price, [Op.gte]: parseFloat(req.query.minPrice) };
    }

    if (req.query.maxPrice) {
      whereClause.price = { ...whereClause.price, [Op.lte]: parseFloat(req.query.maxPrice) };
    }

    console.log('查询条件:', whereClause);

    // 查询服务
    const services = await Service.findAll({
      where: Object.keys(whereClause).length > 0 ? whereClause : undefined,
    });

    res.json(services);
  } catch (error) {
    console.error('获取服务列表失败:', error);
    res.status(500);
    res.json({
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? null : error.stack,
    });
  }
};

// @desc    获取单个服务
// @route   GET /api/services/:id
// @access  Public
const getServiceById = async (req, res) => {
  try {
    const service = await Service.findByPk(req.params.id);

    if (service) {
      res.json(service);
    } else {
      res.status(404);
      throw new Error('服务不存在');
    }
  } catch (error) {
    res.status(res.statusCode === 200 ? 500 : res.statusCode);
    res.json({
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? null : error.stack,
    });
  }
};

// @desc    创建服务
// @route   POST /api/services
// @access  Private/Admin
const createService = async (req, res) => {
  try {
    const { name, description, price, duration, category, isActive } = req.body;

    const serviceExists = await Service.findOne({ where: { name } });

    if (serviceExists) {
      res.status(400);
      throw new Error('服务名称已存在');
    }

    const service = await Service.create({
      name,
      description,
      price,
      duration,
      category,
      isActive,
    });

    if (service) {
      res.status(201).json(service);
    } else {
      res.status(400);
      throw new Error('无效的服务数据');
    }
  } catch (error) {
    res.status(res.statusCode === 200 ? 500 : res.statusCode);
    res.json({
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? null : error.stack,
    });
  }
};

// @desc    更新服务
// @route   PUT /api/services/:id
// @access  Private/Admin
const updateService = async (req, res) => {
  try {
    const { name, description, price, duration, category, isActive } = req.body;

    const service = await Service.findByPk(req.params.id);

    if (service) {
      service.name = name || service.name;
      service.description = description || service.description;
      service.price = price || service.price;
      service.duration = duration || service.duration;
      service.category = category || service.category;
      service.isActive = isActive !== undefined ? isActive : service.isActive;

      const updatedService = await service.save();
      res.json(updatedService);
    } else {
      res.status(404);
      throw new Error('服务不存在');
    }
  } catch (error) {
    res.status(res.statusCode === 200 ? 500 : res.statusCode);
    res.json({
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? null : error.stack,
    });
  }
};

// @desc    删除服务
// @route   DELETE /api/services/:id
// @access  Private/Admin
const deleteService = async (req, res) => {
  try {
    const service = await Service.findByPk(req.params.id);

    if (service) {
      await service.destroy();
      res.json({ message: '服务已删除' });
    } else {
      res.status(404);
      throw new Error('服务不存在');
    }
  } catch (error) {
    res.status(res.statusCode === 200 ? 500 : res.statusCode);
    res.json({
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? null : error.stack,
    });
  }
};

module.exports = {
  getServices,
  getServiceById,
  createService,
  updateService,
  deleteService,
};
