const { User } = require('../models');
const jwt = require('jsonwebtoken');
const { Op } = require('sequelize');

// 生成JWT
const generateToken = (id) => {
  return jwt.sign({ id }, process.env.JWT_SECRET, {
    expiresIn: '30d',
  });
};

// @desc    用户登录
// @route   POST /api/users/login
// @access  Public
const loginUser = async (req, res) => {
  try {
    console.log('登录请求体:', req.body);
    const { email, password } = req.body;

    if (!email || !password) {
      console.log('邮箱或密码为空');
      res.status(400);
      return res.json({
        message: '请提供邮箱和密码'
      });
    }

    console.log(`尝试查找用户: ${email}`);
    let user = await User.findOne({ where: { email } });

    // 如果用户不存在，并且是特定的测试用户，则创建它
    if (!user && email === '<EMAIL>') {
      try {
        console.log('创建测试管理员用户');
        user = await User.create({
          name: '测试管理员',
          email: '<EMAIL>',
          password: '123456', // 会自动通过钩子加密
          role: 'admin',
          phone: '13900000000'
        });
        console.log('测试管理员用户创建成功');

        // 创建用户后直接返回登录成功
        const token = generateToken(user.id);
        console.log(`生成token成功: ${token.substring(0, 20)}...`);

        return res.json({
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          phone: user.phone,
          token: token,
        });
      } catch (err) {
        console.error('创建测试管理员用户失败:', err);
      }
    }

    if (!user) {
      console.log(`用户不存在: ${email}`);
      res.status(401);
      return res.json({
        message: '邮箱或密码不正确'
      });
    }

    console.log('验证密码');
    console.log('数据库中的用户信息:', {
      id: user.id,
      email: user.email,
      role: user.role,
      // 不要打印完整密码，只打印前几个字符
      passwordPreview: user.password ? user.password.substring(0, 10) + '...' : 'null'
    });

    // 尝试直接使用bcrypt比较密码
    const bcrypt = require('bcryptjs');
    const directMatch = await bcrypt.compare(password, user.password);
    console.log(`直接bcrypt比较结果: ${directMatch}`);

    // 使用用户模型的matchPassword方法
    const isMatch = await user.matchPassword(password);
    console.log(`模型matchPassword结果: ${isMatch}`);

    if (isMatch) {
      const token = generateToken(user.id);
      console.log(`生成token成功: ${token.substring(0, 20)}...`);

      return res.json({
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        phone: user.phone,
        token: token,
      });
    } else {
      // 移除自动重置密码的功能，这是一个安全漏洞

      console.log('密码不匹配');
      res.status(401);
      return res.json({
        message: '邮箱或密码不正确'
      });
    }
  } catch (error) {
    console.error('登录过程中出错:', error);
    res.status(res.statusCode === 200 ? 500 : res.statusCode);
    return res.json({
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? null : error.stack,
    });
  }
};

// @desc    注册新用户
// @route   POST /api/users
// @access  Private/Admin
const registerUser = async (req, res) => {
  try {
    const { name, email, password, role, phone } = req.body;

    const userExists = await User.findOne({ where: { email } });

    if (userExists) {
      res.status(400);
      throw new Error('用户已存在');
    }

    const user = await User.create({
      name,
      email,
      password,
      role,
      phone,
    });

    if (user) {
      res.status(201).json({
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        phone: user.phone,
        token: generateToken(user.id),
      });
    } else {
      res.status(400);
      throw new Error('无效的用户数据');
    }
  } catch (error) {
    res.status(res.statusCode === 200 ? 500 : res.statusCode);
    res.json({
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? null : error.stack,
    });
  }
};

// @desc    获取用户资料
// @route   GET /api/users/profile
// @access  Private
const getUserProfile = async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id);

    if (user) {
      res.json({
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        phone: user.phone,
      });
    } else {
      res.status(404);
      throw new Error('用户不存在');
    }
  } catch (error) {
    res.status(res.statusCode === 200 ? 500 : res.statusCode);
    res.json({
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? null : error.stack,
    });
  }
};

// @desc    更新用户资料
// @route   PUT /api/users/profile
// @access  Private
const updateUserProfile = async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id);

    if (user) {
      user.name = req.body.name || user.name;
      user.email = req.body.email || user.email;
      user.phone = req.body.phone || user.phone;

      if (req.body.password) {
        user.password = req.body.password;
      }

      const updatedUser = await user.save();

      res.json({
        id: updatedUser.id,
        name: updatedUser.name,
        email: updatedUser.email,
        role: updatedUser.role,
        phone: updatedUser.phone,
        token: generateToken(updatedUser.id),
      });
    } else {
      res.status(404);
      throw new Error('用户不存在');
    }
  } catch (error) {
    res.status(res.statusCode === 200 ? 500 : res.statusCode);
    res.json({
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? null : error.stack,
    });
  }
};

// @desc    修改密码
// @route   PUT /api/users/profile/password
// @access  Private
const updatePassword = async (req, res) => {
  try {
    const { oldPassword, newPassword, confirmPassword } = req.body;

    // 验证请求参数
    if (!oldPassword || !newPassword || !confirmPassword) {
      res.status(400);
      return res.json({
        success: false,
        message: '请提供当前密码、新密码和确认密码'
      });
    }

    // 验证新密码和确认密码是否一致
    if (newPassword !== confirmPassword) {
      res.status(400);
      return res.json({
        success: false,
        message: '新密码和确认密码不一致'
      });
    }

    // 获取用户信息
    const user = await User.findByPk(req.user.id);
    if (!user) {
      res.status(404);
      return res.json({
        success: false,
        message: '用户不存在'
      });
    }

    // 验证当前密码是否正确
    const isMatch = await user.matchPassword(oldPassword);
    if (!isMatch) {
      res.status(401);
      return res.json({
        success: false,
        message: '当前密码不正确'
      });
    }

    // 更新密码
    user.password = newPassword;
    await user.save();

    res.json({
      success: true,
      message: '密码修改成功'
    });
  } catch (error) {
    console.error('修改密码失败:', error);
    res.status(res.statusCode === 200 ? 500 : res.statusCode);
    res.json({
      success: false,
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? null : error.stack,
    });
  }
};

// @desc    获取所有用户
// @route   GET /api/users
// @access  Private/Admin
const getUsers = async (req, res) => {
  try {
    console.log('查询参数:', req.query);

    // 构建查询条件
    const whereClause = {};

    // 处理搜索参数
    if (req.query.name) {
      whereClause.name = { [Op.like]: `%${req.query.name}%` };
    }

    if (req.query.email) {
      whereClause.email = { [Op.like]: `%${req.query.email}%` };
    }

    if (req.query.phone) {
      whereClause.phone = { [Op.like]: `%${req.query.phone}%` };
    }

    if (req.query.role) {
      whereClause.role = req.query.role;
    }

    console.log('查询条件:', whereClause);

    // 查询用户
    const users = await User.findAll({
      where: Object.keys(whereClause).length > 0 ? whereClause : undefined,
      attributes: { exclude: ['password'] }
    });

    res.json(users);
  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500);
    res.json({
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? null : error.stack,
    });
  }
};

// @desc    删除用户
// @route   DELETE /api/users/:id
// @access  Private/Admin
const deleteUser = async (req, res) => {
  try {
    const user = await User.findByPk(req.params.id);

    if (user) {
      await user.destroy();
      res.json({ message: '用户已删除' });
    } else {
      res.status(404);
      throw new Error('用户不存在');
    }
  } catch (error) {
    res.status(res.statusCode === 200 ? 500 : res.statusCode);
    res.json({
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? null : error.stack,
    });
  }
};

// @desc    获取用户信息
// @route   GET /api/users/:id
// @access  Private/Admin
const getUserById = async (req, res) => {
  try {
    const user = await User.findByPk(req.params.id, {
      attributes: { exclude: ['password'] }
    });

    if (user) {
      res.json(user);
    } else {
      res.status(404);
      throw new Error('用户不存在');
    }
  } catch (error) {
    res.status(res.statusCode === 200 ? 500 : res.statusCode);
    res.json({
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? null : error.stack,
    });
  }
};

// @desc    更新用户
// @route   PUT /api/users/:id
// @access  Private/Admin
const updateUser = async (req, res) => {
  try {
    const user = await User.findByPk(req.params.id);

    if (user) {
      user.name = req.body.name || user.name;
      user.email = req.body.email || user.email;
      user.role = req.body.role || user.role;
      user.phone = req.body.phone || user.phone;

      const updatedUser = await user.save();

      res.json({
        id: updatedUser.id,
        name: updatedUser.name,
        email: updatedUser.email,
        role: updatedUser.role,
        phone: updatedUser.phone,
      });
    } else {
      res.status(404);
      throw new Error('用户不存在');
    }
  } catch (error) {
    res.status(res.statusCode === 200 ? 500 : res.statusCode);
    res.json({
      message: error.message,
      stack: process.env.NODE_ENV === 'production' ? null : error.stack,
    });
  }
};

module.exports = {
  loginUser,
  registerUser,
  getUserProfile,
  updateUserProfile,
  updatePassword,
  getUsers,
  deleteUser,
  getUserById,
  updateUser,
};
