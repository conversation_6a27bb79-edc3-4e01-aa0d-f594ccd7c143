const asyncHandler = require('express-async-handler');
const { Finance, Order, User } = require('../models');
const { Op, Sequelize } = require('sequelize');

// @desc    获取所有财务记录
// @route   GET /api/finances
// @access  Private
const getFinances = asyncHandler(async (req, res) => {
  // 支持日期范围和类型过滤
  const { startDate, endDate, type, category } = req.query;
  
  let whereClause = {};
  
  if (startDate && endDate) {
    whereClause.date = {
      [Op.between]: [startDate, endDate]
    };
  } else if (startDate) {
    whereClause.date = {
      [Op.gte]: startDate
    };
  } else if (endDate) {
    whereClause.date = {
      [Op.lte]: endDate
    };
  }
  
  if (type) {
    whereClause.type = type;
  }
  
  if (category) {
    whereClause.category = category;
  }
  
  const finances = await Finance.findAll({
    where: whereClause,
    include: [
      {
        model: Order,
        as: 'order',
        include: ['service', 'customer']
      },
      {
        model: User,
        as: 'staff',
        attributes: ['id', 'name', 'email', 'phone']
      }
    ],
    order: [['date', 'DESC'], ['createdAt', 'DESC']]
  });
  
  res.json(finances);
});

// @desc    获取单个财务记录
// @route   GET /api/finances/:id
// @access  Private
const getFinanceById = asyncHandler(async (req, res) => {
  const finance = await Finance.findByPk(req.params.id, {
    include: [
      {
        model: Order,
        as: 'order',
        include: ['service', 'customer']
      },
      {
        model: User,
        as: 'staff',
        attributes: ['id', 'name', 'email', 'phone']
      }
    ]
  });
  
  if (finance) {
    res.json(finance);
  } else {
    res.status(404);
    throw new Error('财务记录不存在');
  }
});

// @desc    创建财务记录
// @route   POST /api/finances
// @access  Private
const createFinance = asyncHandler(async (req, res) => {
  const { 
    type, 
    category, 
    amount, 
    date, 
    description, 
    orderId, 
    staffId, 
    paymentMethod, 
    status 
  } = req.body;

  // 验证必填字段
  if (!type || !category || !amount || !date) {
    res.status(400);
    throw new Error('请提供类型、类别、金额和日期');
  }

  // 如果有订单ID，检查订单是否存在
  if (orderId) {
    const order = await Order.findByPk(orderId);
    if (!order) {
      res.status(404);
      throw new Error('订单不存在');
    }
  }

  // 如果有员工ID，检查员工是否存在
  if (staffId) {
    const staff = await User.findByPk(staffId);
    if (!staff) {
      res.status(404);
      throw new Error('员工不存在');
    }
  }

  const finance = await Finance.create({
    type,
    category,
    amount,
    date,
    description,
    orderId,
    staffId,
    paymentMethod,
    status: status || 'completed'
  });

  if (finance) {
    res.status(201).json(finance);
  } else {
    res.status(400);
    throw new Error('无效的财务数据');
  }
});

// @desc    更新财务记录
// @route   PUT /api/finances/:id
// @access  Private
const updateFinance = asyncHandler(async (req, res) => {
  const finance = await Finance.findByPk(req.params.id);
  
  if (finance) {
    const { 
      type, 
      category, 
      amount, 
      date, 
      description, 
      orderId, 
      staffId, 
      paymentMethod, 
      status 
    } = req.body;
    
    // 如果更改了订单ID，检查订单是否存在
    if (orderId && orderId !== finance.orderId) {
      const order = await Order.findByPk(orderId);
      if (!order) {
        res.status(404);
        throw new Error('订单不存在');
      }
    }
    
    // 如果更改了员工ID，检查员工是否存在
    if (staffId && staffId !== finance.staffId) {
      const staff = await User.findByPk(staffId);
      if (!staff) {
        res.status(404);
        throw new Error('员工不存在');
      }
    }
    
    finance.type = type || finance.type;
    finance.category = category || finance.category;
    finance.amount = amount || finance.amount;
    finance.date = date || finance.date;
    finance.description = description !== undefined ? description : finance.description;
    finance.orderId = orderId !== undefined ? orderId : finance.orderId;
    finance.staffId = staffId !== undefined ? staffId : finance.staffId;
    finance.paymentMethod = paymentMethod || finance.paymentMethod;
    finance.status = status || finance.status;
    
    const updatedFinance = await finance.save();
    res.json(updatedFinance);
  } else {
    res.status(404);
    throw new Error('财务记录不存在');
  }
});

// @desc    删除财务记录
// @route   DELETE /api/finances/:id
// @access  Private
const deleteFinance = asyncHandler(async (req, res) => {
  const finance = await Finance.findByPk(req.params.id);
  
  if (finance) {
    await finance.destroy();
    res.json({ message: '财务记录已删除' });
  } else {
    res.status(404);
    throw new Error('财务记录不存在');
  }
});

// @desc    获取财务统计
// @route   GET /api/finances/stats
// @access  Private
const getFinanceStats = asyncHandler(async (req, res) => {
  const { startDate, endDate } = req.query;
  
  let whereClause = {};
  
  if (startDate && endDate) {
    whereClause.date = {
      [Op.between]: [startDate, endDate]
    };
  } else if (startDate) {
    whereClause.date = {
      [Op.gte]: startDate
    };
  } else if (endDate) {
    whereClause.date = {
      [Op.lte]: endDate
    };
  }
  
  // 总收入
  const totalIncome = await Finance.sum('amount', {
    where: {
      ...whereClause,
      type: 'income',
      status: 'completed'
    }
  });
  
  // 总支出
  const totalExpense = await Finance.sum('amount', {
    where: {
      ...whereClause,
      type: 'expense',
      status: 'completed'
    }
  });
  
  // 按类别统计
  const categoryStats = await Finance.findAll({
    attributes: [
      'category',
      'type',
      [Sequelize.fn('SUM', Sequelize.col('amount')), 'total']
    ],
    where: {
      ...whereClause,
      status: 'completed'
    },
    group: ['category', 'type'],
    order: [['type', 'ASC'], [Sequelize.literal('total'), 'DESC']]
  });
  
  // 按月统计
  const monthlyStats = await Finance.findAll({
    attributes: [
      [Sequelize.fn('DATE_FORMAT', Sequelize.col('date'), '%Y-%m'), 'month'],
      'type',
      [Sequelize.fn('SUM', Sequelize.col('amount')), 'total']
    ],
    where: {
      ...whereClause,
      status: 'completed'
    },
    group: [Sequelize.fn('DATE_FORMAT', Sequelize.col('date'), '%Y-%m'), 'type'],
    order: [[Sequelize.fn('DATE_FORMAT', Sequelize.col('date'), '%Y-%m'), 'ASC'], ['type', 'ASC']]
  });
  
  res.json({
    totalIncome: totalIncome || 0,
    totalExpense: totalExpense || 0,
    netProfit: (totalIncome || 0) - (totalExpense || 0),
    categoryStats,
    monthlyStats
  });
});

module.exports = {
  getFinances,
  getFinanceById,
  createFinance,
  updateFinance,
  deleteFinance,
  getFinanceStats
};
