const asyncHandler = require('express-async-handler');
const { Order, Service, User, Customer, Review, Finance } = require('../models');
const { Op, Sequelize } = require('sequelize');

// @desc    获取仪表盘统计数据
// @route   GET /api/stats/dashboard
// @access  Private
const getDashboardStats = asyncHandler(async (req, res) => {
  try {
    // 获取总订单数
    let totalOrders = 0;
    try {
      totalOrders = await Order.count();
    } catch (error) {
      console.error('获取总订单数失败:', error.message);
    }

    // 获取待处理订单数
    let pendingOrders = 0;
    try {
      pendingOrders = await Order.count({
        where: {
          status: {
            [Op.in]: ['pending', 'confirmed']
          }
        }
      });
    } catch (error) {
      console.error('获取待处理订单数失败:', error.message);
    }

    // 获取已完成订单数
    let completedOrders = 0;
    try {
      completedOrders = await Order.count({
        where: {
          status: 'completed'
        }
      });
    } catch (error) {
      console.error('获取已完成订单数失败:', error.message);
    }

    // 获取员工数量
    let totalStaff = 0;
    try {
      totalStaff = await User.count({
        where: {
          role: 'staff'
        }
      });
    } catch (error) {
      console.error('获取员工数量失败:', error.message);
    }

    // 获取客户数量
    let totalCustomers = 0;
    try {
      totalCustomers = await Customer.count();
    } catch (error) {
      console.error('获取客户数量失败:', error.message);
    }

    // 获取服务数量
    let totalServices = 0;
    try {
      totalServices = await Service.count();
    } catch (error) {
      console.error('获取服务数量失败:', error.message);
    }

    // 获取本月收入
    const currentDate = new Date();
    const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

    let monthlyIncome = 0;
    try {
      monthlyIncome = await Finance.sum('amount', {
        where: {
          type: 'income',
          status: 'completed',
          date: {
            [Op.between]: [firstDayOfMonth, lastDayOfMonth]
          }
        }
      }) || 0;
    } catch (error) {
      console.error('获取本月收入失败:', error.message);
    }

    // 获取本月支出
    let monthlyExpense = 0;
    try {
      monthlyExpense = await Finance.sum('amount', {
        where: {
          type: 'expense',
          status: 'completed',
          date: {
            [Op.between]: [firstDayOfMonth, lastDayOfMonth]
          }
        }
      }) || 0;
    } catch (error) {
      console.error('获取本月支出失败:', error.message);
    }

    // 获取平均评分
    let averageRating = 0;
    try {
      const ratingResult = await Review.findOne({
        attributes: [
          [Sequelize.fn('AVG', Sequelize.col('rating')), 'avgRating']
        ]
      });
      if (ratingResult) {
        averageRating = ratingResult.getDataValue('avgRating') || 0;
      }
    } catch (error) {
      console.error('获取平均评分失败:', error.message);
    }

    // 获取最近的订单
    let recentOrders = [];
    try {
      recentOrders = await Order.findAll({
        limit: 5,
        order: [['createdAt', 'DESC']],
        include: ['service', 'assignedStaff', 'customer']
      });
    } catch (error) {
      console.error('获取最近订单失败:', error.message);
    }

    res.json({
      totalOrders,
      pendingOrders,
      completedOrders,
      totalStaff,
      totalCustomers,
      totalServices,
      monthlyIncome,
      monthlyExpense,
      monthlyProfit: monthlyIncome - monthlyExpense,
      averageRating,
      recentOrders
    });
  } catch (error) {
    console.error('获取仪表盘统计数据失败:', error);
    res.status(500).json({ message: '获取仪表盘统计数据失败', error: error.message });
  }
});

// @desc    获取订单统计数据
// @route   GET /api/stats/orders
// @access  Private
const getOrderStats = asyncHandler(async (req, res) => {
  const { startDate, endDate } = req.query;

  let whereClause = {};

  if (startDate && endDate) {
    whereClause.createdAt = {
      [Op.between]: [new Date(startDate), new Date(endDate)]
    };
  } else if (startDate) {
    whereClause.createdAt = {
      [Op.gte]: new Date(startDate)
    };
  } else if (endDate) {
    whereClause.createdAt = {
      [Op.lte]: new Date(endDate)
    };
  }

  // 按状态统计订单数量
  const ordersByStatus = await Order.findAll({
    attributes: [
      'status',
      [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
    ],
    where: whereClause,
    group: ['status']
  });

  // 按服务类型统计订单数量
  const ordersByService = await Order.findAll({
    attributes: [
      'serviceId',
      [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
    ],
    where: whereClause,
    include: [{
      model: Service,
      as: 'service',
      attributes: ['name']
    }],
    group: ['serviceId']
  });

  // 按月统计订单数量
  const ordersByMonth = await Order.findAll({
    attributes: [
      [Sequelize.fn('DATE_FORMAT', Sequelize.col('createdAt'), '%Y-%m'), 'month'],
      [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
    ],
    where: whereClause,
    group: [Sequelize.fn('DATE_FORMAT', Sequelize.col('createdAt'), '%Y-%m')],
    order: [[Sequelize.fn('DATE_FORMAT', Sequelize.col('createdAt'), '%Y-%m'), 'ASC']]
  });

  // 按员工统计订单数量
  const ordersByStaff = await Order.findAll({
    attributes: [
      'assignedStaffId',
      [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
    ],
    where: {
      ...whereClause,
      assignedStaffId: {
        [Op.ne]: null
      }
    },
    include: [{
      model: User,
      as: 'assignedStaff',
      attributes: ['name']
    }],
    group: ['assignedStaffId']
  });

  res.json({
    ordersByStatus,
    ordersByService,
    ordersByMonth,
    ordersByStaff
  });
});

// @desc    获取员工绩效统计
// @route   GET /api/stats/staff-performance
// @access  Private
const getStaffPerformance = asyncHandler(async (req, res) => {
  const { startDate, endDate } = req.query;

  let whereClause = {};

  if (startDate && endDate) {
    whereClause.createdAt = {
      [Op.between]: [new Date(startDate), new Date(endDate)]
    };
  } else if (startDate) {
    whereClause.createdAt = {
      [Op.gte]: new Date(startDate)
    };
  } else if (endDate) {
    whereClause.createdAt = {
      [Op.lte]: new Date(endDate)
    };
  }

  // 获取所有员工
  const staffMembers = await User.findAll({
    where: {
      role: 'staff'
    },
    attributes: ['id', 'name']
  });

  const staffPerformance = [];

  // 为每个员工计算绩效指标
  for (const staff of staffMembers) {
    // 完成的订单数
    const completedOrders = await Order.count({
      where: {
        ...whereClause,
        assignedStaffId: staff.id,
        status: 'completed'
      }
    });

    // 取消的订单数
    const cancelledOrders = await Order.count({
      where: {
        ...whereClause,
        assignedStaffId: staff.id,
        status: 'cancelled'
      }
    });

    // 平均评分
    const avgRating = await Review.findOne({
      attributes: [
        [Sequelize.fn('AVG', Sequelize.col('rating')), 'avgRating']
      ],
      where: {
        ...whereClause,
        staffId: staff.id
      }
    });

    // 服务时长
    const totalServiceHours = await Order.sum('duration', {
      where: {
        ...whereClause,
        assignedStaffId: staff.id,
        status: 'completed'
      }
    });

    // 收入贡献
    const revenueContribution = await Finance.sum('amount', {
      where: {
        ...whereClause,
        staffId: staff.id,
        type: 'income',
        status: 'completed'
      }
    });

    staffPerformance.push({
      staffId: staff.id,
      staffName: staff.name,
      completedOrders: completedOrders || 0,
      cancelledOrders: cancelledOrders || 0,
      avgRating: avgRating.getDataValue('avgRating') || 0,
      totalServiceHours: totalServiceHours || 0,
      revenueContribution: revenueContribution || 0
    });
  }

  res.json(staffPerformance);
});

module.exports = {
  getDashboardStats,
  getOrderStats,
  getStaffPerformance
};
