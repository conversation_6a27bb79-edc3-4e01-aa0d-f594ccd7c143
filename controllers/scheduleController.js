const asyncHandler = require('express-async-handler');
const { Schedule, User, Order } = require('../models');
const { Op } = require('sequelize');

// @desc    获取所有排班
// @route   GET /api/schedules
// @access  Private
const getSchedules = asyncHandler(async (req, res) => {
  // 支持日期范围过滤
  const { startDate, endDate, staffId } = req.query;
  
  let whereClause = {};
  
  if (startDate && endDate) {
    whereClause.date = {
      [Op.between]: [startDate, endDate]
    };
  } else if (startDate) {
    whereClause.date = {
      [Op.gte]: startDate
    };
  } else if (endDate) {
    whereClause.date = {
      [Op.lte]: endDate
    };
  }
  
  if (staffId) {
    whereClause.staffId = staffId;
  }
  
  const schedules = await Schedule.findAll({
    where: whereClause,
    include: [
      {
        model: User,
        as: 'staff',
        attributes: ['id', 'name', 'email', 'phone']
      },
      {
        model: Order,
        as: 'order',
        include: ['service', 'customer']
      }
    ],
    order: [['date', 'ASC'], ['startTime', 'ASC']]
  });
  
  res.json(schedules);
});

// @desc    获取单个排班
// @route   GET /api/schedules/:id
// @access  Private
const getScheduleById = asyncHandler(async (req, res) => {
  const schedule = await Schedule.findByPk(req.params.id, {
    include: [
      {
        model: User,
        as: 'staff',
        attributes: ['id', 'name', 'email', 'phone']
      },
      {
        model: Order,
        as: 'order',
        include: ['service', 'customer']
      }
    ]
  });
  
  if (schedule) {
    res.json(schedule);
  } else {
    res.status(404);
    throw new Error('排班不存在');
  }
});

// @desc    创建排班
// @route   POST /api/schedules
// @access  Private
const createSchedule = asyncHandler(async (req, res) => {
  const { 
    staffId, 
    date, 
    startTime, 
    endTime, 
    status, 
    orderId, 
    notes 
  } = req.body;

  // 检查员工是否存在
  const staff = await User.findByPk(staffId);
  if (!staff) {
    res.status(404);
    throw new Error('员工不存在');
  }

  // 如果有订单ID，检查订单是否存在
  if (orderId) {
    const order = await Order.findByPk(orderId);
    if (!order) {
      res.status(404);
      throw new Error('订单不存在');
    }
  }

  // 检查时间冲突
  const conflictingSchedule = await Schedule.findOne({
    where: {
      staffId,
      date,
      [Op.or]: [
        {
          // 新排班的开始时间在现有排班的时间范围内
          startTime: {
            [Op.between]: [startTime, endTime]
          }
        },
        {
          // 新排班的结束时间在现有排班的时间范围内
          endTime: {
            [Op.between]: [startTime, endTime]
          }
        },
        {
          // 新排班完全包含现有排班
          [Op.and]: [
            { startTime: { [Op.lte]: startTime } },
            { endTime: { [Op.gte]: endTime } }
          ]
        }
      ]
    }
  });

  if (conflictingSchedule) {
    res.status(400);
    throw new Error('该时间段已有排班安排');
  }

  const schedule = await Schedule.create({
    staffId,
    date,
    startTime,
    endTime,
    status: status || 'scheduled',
    orderId,
    notes
  });

  if (schedule) {
    res.status(201).json(schedule);
  } else {
    res.status(400);
    throw new Error('无效的排班数据');
  }
});

// @desc    更新排班
// @route   PUT /api/schedules/:id
// @access  Private
const updateSchedule = asyncHandler(async (req, res) => {
  const schedule = await Schedule.findByPk(req.params.id);
  
  if (schedule) {
    const { 
      date, 
      startTime, 
      endTime, 
      status, 
      orderId, 
      notes 
    } = req.body;
    
    // 如果更改了日期或时间，检查冲突
    if ((date && date !== schedule.date) || 
        (startTime && startTime !== schedule.startTime) || 
        (endTime && endTime !== schedule.endTime)) {
      
      const newDate = date || schedule.date;
      const newStartTime = startTime || schedule.startTime;
      const newEndTime = endTime || schedule.endTime;
      
      const conflictingSchedule = await Schedule.findOne({
        where: {
          id: { [Op.ne]: req.params.id },
          staffId: schedule.staffId,
          date: newDate,
          [Op.or]: [
            {
              startTime: {
                [Op.between]: [newStartTime, newEndTime]
              }
            },
            {
              endTime: {
                [Op.between]: [newStartTime, newEndTime]
              }
            },
            {
              [Op.and]: [
                { startTime: { [Op.lte]: newStartTime } },
                { endTime: { [Op.gte]: newEndTime } }
              ]
            }
          ]
        }
      });
      
      if (conflictingSchedule) {
        res.status(400);
        throw new Error('该时间段已有排班安排');
      }
    }
    
    // 如果更改了订单ID，检查订单是否存在
    if (orderId && orderId !== schedule.orderId) {
      const order = await Order.findByPk(orderId);
      if (!order) {
        res.status(404);
        throw new Error('订单不存在');
      }
    }
    
    schedule.date = date || schedule.date;
    schedule.startTime = startTime || schedule.startTime;
    schedule.endTime = endTime || schedule.endTime;
    schedule.status = status || schedule.status;
    schedule.orderId = orderId !== undefined ? orderId : schedule.orderId;
    schedule.notes = notes !== undefined ? notes : schedule.notes;
    
    const updatedSchedule = await schedule.save();
    res.json(updatedSchedule);
  } else {
    res.status(404);
    throw new Error('排班不存在');
  }
});

// @desc    删除排班
// @route   DELETE /api/schedules/:id
// @access  Private
const deleteSchedule = asyncHandler(async (req, res) => {
  const schedule = await Schedule.findByPk(req.params.id);
  
  if (schedule) {
    // 如果排班已关联订单，不允许删除
    if (schedule.orderId) {
      res.status(400);
      throw new Error('该排班已关联订单，无法删除');
    }
    
    await schedule.destroy();
    res.json({ message: '排班已删除' });
  } else {
    res.status(404);
    throw new Error('排班不存在');
  }
});

// @desc    获取员工的排班
// @route   GET /api/schedules/staff/:id
// @access  Private
const getStaffSchedules = asyncHandler(async (req, res) => {
  const staffId = req.params.id;
  const { startDate, endDate } = req.query;
  
  // 检查员工是否存在
  const staff = await User.findByPk(staffId);
  if (!staff) {
    res.status(404);
    throw new Error('员工不存在');
  }
  
  let whereClause = { staffId };
  
  if (startDate && endDate) {
    whereClause.date = {
      [Op.between]: [startDate, endDate]
    };
  } else if (startDate) {
    whereClause.date = {
      [Op.gte]: startDate
    };
  } else if (endDate) {
    whereClause.date = {
      [Op.lte]: endDate
    };
  }
  
  const schedules = await Schedule.findAll({
    where: whereClause,
    include: [
      {
        model: Order,
        as: 'order',
        include: ['service', 'customer']
      }
    ],
    order: [['date', 'ASC'], ['startTime', 'ASC']]
  });
  
  res.json(schedules);
});

module.exports = {
  getSchedules,
  getScheduleById,
  createSchedule,
  updateSchedule,
  deleteSchedule,
  getStaffSchedules
};
