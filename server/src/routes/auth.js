const express = require('express');
const Joi = require('joi');
const rateLimit = require('express-rate-limit');

const { query, get, run } = require('../utils/database');
const { generateSmsCode, createSmsService, validatePhoneNumber, validateSmsCode } = require('../utils/sms');
const { hashPassword, verifyPassword, generateToken, generateTokenHash, validatePassword, validateNickname } = require('../utils/auth');
const { authenticateToken } = require('../middleware/auth');
const { 
  asyncHandler, 
  ValidationError, 
  ConflictError, 
  UnauthorizedError,
  NotFoundError 
} = require('../middleware/errorHandler');

const router = express.Router();
const smsService = createSmsService();

// 短信发送限制 - 每个手机号每分钟最多1条
const smsLimiter = rateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 1,
  keyGenerator: (req) => req.body.phone,
  message: {
    error: '发送短信过于频繁，请1分钟后再试'
  }
});

// 登录限制 - 每个IP每15分钟最多10次登录尝试
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 10,
  message: {
    error: '登录尝试过于频繁，请15分钟后再试'
  }
});

// 发送短信验证码
router.post('/send-sms', smsLimiter, asyncHandler(async (req, res) => {
  const schema = Joi.object({
    phone: Joi.string().required().messages({
      'any.required': '手机号不能为空'
    }),
    type: Joi.string().valid('login', 'register').default('login').messages({
      'any.only': '短信类型无效'
    })
  });

  const { error, value } = schema.validate(req.body);
  if (error) {
    throw new ValidationError(error.details[0].message);
  }

  const { phone, type } = value;

  if (!validatePhoneNumber(phone)) {
    throw new ValidationError('手机号格式不正确');
  }

  // 检查用户是否存在
  const existingUser = await get('SELECT id FROM users WHERE phone = ?', [phone]);
  
  if (type === 'register' && existingUser) {
    throw new ConflictError('手机号已注册');
  }
  
  if (type === 'login' && !existingUser) {
    throw new NotFoundError('手机号未注册');
  }

  // 生成验证码
  const code = generateSmsCode();
  const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5分钟后过期

  // 保存验证码到数据库
  await run(
    'INSERT INTO sms_codes (phone, code, type, expires_at) VALUES (?, ?, ?, ?)',
    [phone, code, type, expiresAt.toISOString()]
  );

  // 发送短信
  try {
    await smsService.sendSms(phone, code, type);
    
    res.json({
      success: true,
      message: '验证码发送成功',
      expiresIn: 300 // 5分钟
    });
  } catch (error) {
    console.error('短信发送失败:', error);
    throw new Error('短信发送失败，请稍后重试');
  }
}));

// 验证码登录
router.post('/login-sms', loginLimiter, asyncHandler(async (req, res) => {
  const schema = Joi.object({
    phone: Joi.string().required().messages({
      'any.required': '手机号不能为空'
    }),
    code: Joi.string().required().messages({
      'any.required': '验证码不能为空'
    }),
    deviceInfo: Joi.string().optional()
  });

  const { error, value } = schema.validate(req.body);
  if (error) {
    throw new ValidationError(error.details[0].message);
  }

  const { phone, code, deviceInfo } = value;

  if (!validatePhoneNumber(phone)) {
    throw new ValidationError('手机号格式不正确');
  }

  if (!validateSmsCode(code)) {
    throw new ValidationError('验证码格式不正确');
  }

  // 验证短信验证码
  const smsCode = await get(
    'SELECT * FROM sms_codes WHERE phone = ? AND code = ? AND type = "login" AND used = 0 AND expires_at > datetime("now") ORDER BY created_at DESC LIMIT 1',
    [phone, code]
  );

  if (!smsCode) {
    throw new UnauthorizedError('验证码无效或已过期');
  }

  // 标记验证码为已使用
  await run('UPDATE sms_codes SET used = 1 WHERE id = ?', [smsCode.id]);

  // 查找用户
  const user = await get('SELECT * FROM users WHERE phone = ?', [phone]);
  if (!user) {
    throw new NotFoundError('用户不存在');
  }

  // 生成JWT token
  const token = generateToken({ userId: user.id, phone: user.phone });
  const tokenHash = generateTokenHash(token);
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7天后过期

  // 保存session到数据库
  await run(
    'INSERT INTO user_sessions (user_id, token_hash, device_info, expires_at) VALUES (?, ?, ?, ?)',
    [user.id, tokenHash, deviceInfo || '', expiresAt.toISOString()]
  );

  res.json({
    success: true,
    message: '登录成功',
    data: {
      token,
      user: {
        id: user.id,
        phone: user.phone,
        nickname: user.nickname,
        avatar_url: user.avatar_url,
        is_verified: user.is_verified,
        created_at: user.created_at
      }
    }
  });
}));

// 密码登录
router.post('/login-password', loginLimiter, asyncHandler(async (req, res) => {
  const schema = Joi.object({
    phone: Joi.string().required().messages({
      'any.required': '手机号不能为空'
    }),
    password: Joi.string().required().messages({
      'any.required': '密码不能为空'
    }),
    deviceInfo: Joi.string().optional()
  });

  const { error, value } = schema.validate(req.body);
  if (error) {
    throw new ValidationError(error.details[0].message);
  }

  const { phone, password, deviceInfo } = value;

  if (!validatePhoneNumber(phone)) {
    throw new ValidationError('手机号格式不正确');
  }

  // 查找用户
  const user = await get('SELECT * FROM users WHERE phone = ?', [phone]);
  if (!user) {
    throw new UnauthorizedError('手机号或密码错误');
  }

  if (!user.password_hash) {
    throw new UnauthorizedError('该账号未设置密码，请使用验证码登录');
  }

  // 验证密码
  const isPasswordValid = await verifyPassword(password, user.password_hash);
  if (!isPasswordValid) {
    throw new UnauthorizedError('手机号或密码错误');
  }

  // 生成JWT token
  const token = generateToken({ userId: user.id, phone: user.phone });
  const tokenHash = generateTokenHash(token);
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7天后过期

  // 保存session到数据库
  await run(
    'INSERT INTO user_sessions (user_id, token_hash, device_info, expires_at) VALUES (?, ?, ?, ?)',
    [user.id, tokenHash, deviceInfo || '', expiresAt.toISOString()]
  );

  res.json({
    success: true,
    message: '登录成功',
    data: {
      token,
      user: {
        id: user.id,
        phone: user.phone,
        nickname: user.nickname,
        avatar_url: user.avatar_url,
        is_verified: user.is_verified,
        created_at: user.created_at
      }
    }
  });
}));

// 注册
router.post('/register', asyncHandler(async (req, res) => {
  const schema = Joi.object({
    phone: Joi.string().required().messages({
      'any.required': '手机号不能为空'
    }),
    code: Joi.string().required().messages({
      'any.required': '验证码不能为空'
    }),
    password: Joi.string().optional(),
    nickname: Joi.string().required().messages({
      'any.required': '昵称不能为空'
    }),
    deviceInfo: Joi.string().optional()
  });

  const { error, value } = schema.validate(req.body);
  if (error) {
    throw new ValidationError(error.details[0].message);
  }

  const { phone, code, password, nickname, deviceInfo } = value;

  if (!validatePhoneNumber(phone)) {
    throw new ValidationError('手机号格式不正确');
  }

  if (!validateSmsCode(code)) {
    throw new ValidationError('验证码格式不正确');
  }

  // 验证昵称
  const nicknameValidation = validateNickname(nickname);
  if (!nicknameValidation.valid) {
    throw new ValidationError(nicknameValidation.message);
  }

  // 验证密码（如果提供）
  if (password) {
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.valid) {
      throw new ValidationError(passwordValidation.message);
    }
  }

  // 验证短信验证码
  const smsCode = await get(
    'SELECT * FROM sms_codes WHERE phone = ? AND code = ? AND type = "register" AND used = 0 AND expires_at > datetime("now") ORDER BY created_at DESC LIMIT 1',
    [phone, code]
  );

  if (!smsCode) {
    throw new UnauthorizedError('验证码无效或已过期');
  }

  // 检查用户是否已存在
  const existingUser = await get('SELECT id FROM users WHERE phone = ?', [phone]);
  if (existingUser) {
    throw new ConflictError('手机号已注册');
  }

  // 标记验证码为已使用
  await run('UPDATE sms_codes SET used = 1 WHERE id = ?', [smsCode.id]);

  // 创建用户
  const passwordHash = password ? await hashPassword(password) : null;
  const result = await run(
    'INSERT INTO users (phone, password_hash, nickname, is_verified) VALUES (?, ?, ?, 1)',
    [phone, passwordHash, nickname]
  );

  const userId = result.lastID;

  // 生成JWT token
  const token = generateToken({ userId, phone });
  const tokenHash = generateTokenHash(token);
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7天后过期

  // 保存session到数据库
  await run(
    'INSERT INTO user_sessions (user_id, token_hash, device_info, expires_at) VALUES (?, ?, ?, ?)',
    [userId, tokenHash, deviceInfo || '', expiresAt.toISOString()]
  );

  res.status(201).json({
    success: true,
    message: '注册成功',
    data: {
      token,
      user: {
        id: userId,
        phone,
        nickname,
        avatar_url: null,
        is_verified: true,
        created_at: new Date().toISOString()
      }
    }
  });
}));

// 获取用户信息
router.get('/profile', authenticateToken, asyncHandler(async (req, res) => {
  res.json({
    success: true,
    data: {
      user: req.user
    }
  });
}));

// 更新用户信息
router.put('/profile', authenticateToken, asyncHandler(async (req, res) => {
  const schema = Joi.object({
    nickname: Joi.string().optional(),
    avatar_url: Joi.string().uri().optional().allow('')
  });

  const { error, value } = schema.validate(req.body);
  if (error) {
    throw new ValidationError(error.details[0].message);
  }

  const { nickname, avatar_url } = value;
  const updates = [];
  const params = [];

  if (nickname !== undefined) {
    const nicknameValidation = validateNickname(nickname);
    if (!nicknameValidation.valid) {
      throw new ValidationError(nicknameValidation.message);
    }
    updates.push('nickname = ?');
    params.push(nickname);
  }

  if (avatar_url !== undefined) {
    updates.push('avatar_url = ?');
    params.push(avatar_url);
  }

  if (updates.length === 0) {
    throw new ValidationError('没有需要更新的字段');
  }

  updates.push('updated_at = datetime("now")');
  params.push(req.user.id);

  await run(
    `UPDATE users SET ${updates.join(', ')} WHERE id = ?`,
    params
  );

  // 获取更新后的用户信息
  const updatedUser = await get(
    'SELECT id, phone, nickname, avatar_url, is_verified, created_at FROM users WHERE id = ?',
    [req.user.id]
  );

  res.json({
    success: true,
    message: '用户信息更新成功',
    data: {
      user: updatedUser
    }
  });
}));

// 设置密码
router.post('/set-password', authenticateToken, asyncHandler(async (req, res) => {
  const schema = Joi.object({
    password: Joi.string().required().messages({
      'any.required': '密码不能为空'
    }),
    code: Joi.string().required().messages({
      'any.required': '验证码不能为空'
    })
  });

  const { error, value } = schema.validate(req.body);
  if (error) {
    throw new ValidationError(error.details[0].message);
  }

  const { password, code } = value;

  // 验证密码
  const passwordValidation = validatePassword(password);
  if (!passwordValidation.valid) {
    throw new ValidationError(passwordValidation.message);
  }

  if (!validateSmsCode(code)) {
    throw new ValidationError('验证码格式不正确');
  }

  // 验证短信验证码
  const smsCode = await get(
    'SELECT * FROM sms_codes WHERE phone = ? AND code = ? AND used = 0 AND expires_at > datetime("now") ORDER BY created_at DESC LIMIT 1',
    [req.user.phone, code]
  );

  if (!smsCode) {
    throw new UnauthorizedError('验证码无效或已过期');
  }

  // 标记验证码为已使用
  await run('UPDATE sms_codes SET used = 1 WHERE id = ?', [smsCode.id]);

  // 更新密码
  const passwordHash = await hashPassword(password);
  await run(
    'UPDATE users SET password_hash = ?, updated_at = datetime("now") WHERE id = ?',
    [passwordHash, req.user.id]
  );

  res.json({
    success: true,
    message: '密码设置成功'
  });
}));

// 登出
router.post('/logout', authenticateToken, asyncHandler(async (req, res) => {
  // 删除当前session
  await run('DELETE FROM user_sessions WHERE id = ?', [req.sessionId]);

  res.json({
    success: true,
    message: '登出成功'
  });
}));

// 登出所有设备
router.post('/logout-all', authenticateToken, asyncHandler(async (req, res) => {
  // 删除用户的所有session
  await run('DELETE FROM user_sessions WHERE user_id = ?', [req.user.id]);

  res.json({
    success: true,
    message: '已登出所有设备'
  });
}));

module.exports = router;
