const crypto = require('crypto');

// 生成6位数字验证码
function generateSmsCode() {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// 模拟短信发送服务
class MockSmsService {
  async sendSms(phone, code, type = 'login') {
    console.log(`[模拟短信] 发送到 ${phone}: 验证码 ${code} (类型: ${type})`);
    
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 模拟发送成功
    return {
      success: true,
      messageId: crypto.randomUUID(),
      message: '短信发送成功'
    };
  }
}

// 阿里云短信服务 (示例实现，需要安装 @alicloud/sms20170525)
class AliyunSmsService {
  constructor() {
    // 在生产环境中，这些配置应该从环境变量中读取
    this.accessKeyId = process.env.ALIYUN_ACCESS_KEY_ID;
    this.accessKeySecret = process.env.ALIYUN_ACCESS_KEY_SECRET;
    this.signName = process.env.ALIYUN_SMS_SIGN_NAME;
    this.templateCode = process.env.ALIYUN_SMS_TEMPLATE_CODE;
  }

  async sendSms(phone, code, type = 'login') {
    try {
      // 这里应该调用阿里云SMS SDK
      // const client = new Dysmsapi20170525(config);
      // const sendSmsRequest = new $Dysmsapi20170525.SendSmsRequest({
      //   phoneNumbers: phone,
      //   signName: this.signName,
      //   templateCode: this.templateCode,
      //   templateParam: JSON.stringify({ code })
      // });
      // const response = await client.sendSms(sendSmsRequest);
      
      console.log(`[阿里云短信] 发送到 ${phone}: 验证码 ${code} (类型: ${type})`);
      
      return {
        success: true,
        messageId: crypto.randomUUID(),
        message: '短信发送成功'
      };
    } catch (error) {
      console.error('阿里云短信发送失败:', error);
      throw new Error('短信发送失败');
    }
  }
}

// 短信服务工厂
function createSmsService() {
  const provider = process.env.SMS_PROVIDER || 'mock';
  
  switch (provider) {
    case 'aliyun':
      return new AliyunSmsService();
    case 'mock':
    default:
      return new MockSmsService();
  }
}

// 验证手机号格式
function validatePhoneNumber(phone) {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
}

// 验证验证码格式
function validateSmsCode(code) {
  const codeRegex = /^\d{6}$/;
  return codeRegex.test(code);
}

module.exports = {
  generateSmsCode,
  createSmsService,
  validatePhoneNumber,
  validateSmsCode
};
