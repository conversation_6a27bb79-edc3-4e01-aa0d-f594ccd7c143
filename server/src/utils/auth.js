const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');

const JWT_SECRET = process.env.JWT_SECRET || 'your-fallback-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';
const BCRYPT_ROUNDS = parseInt(process.env.BCRYPT_ROUNDS) || 12;

// 密码加密
async function hashPassword(password) {
  return await bcrypt.hash(password, BCRYPT_ROUNDS);
}

// 密码验证
async function verifyPassword(password, hashedPassword) {
  return await bcrypt.compare(password, hashedPassword);
}

// 生成JWT token
function generateToken(payload) {
  return jwt.sign(payload, JWT_SECRET, { 
    expiresIn: JWT_EXPIRES_IN,
    issuer: 'home2-app',
    audience: 'home2-users'
  });
}

// 验证JWT token
function verifyToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET, {
      issuer: 'home2-app',
      audience: 'home2-users'
    });
  } catch (error) {
    throw new Error('无效的token');
  }
}

// 生成token哈希值（用于数据库存储）
function generateTokenHash(token) {
  return crypto.createHash('sha256').update(token).digest('hex');
}

// 生成随机字符串
function generateRandomString(length = 32) {
  return crypto.randomBytes(length).toString('hex');
}

// 验证密码强度
function validatePassword(password) {
  if (!password || password.length < 6) {
    return { valid: false, message: '密码长度至少6位' };
  }
  
  if (password.length > 50) {
    return { valid: false, message: '密码长度不能超过50位' };
  }
  
  // 检查是否包含至少一个数字和一个字母
  const hasNumber = /\d/.test(password);
  const hasLetter = /[a-zA-Z]/.test(password);
  
  if (!hasNumber || !hasLetter) {
    return { valid: false, message: '密码必须包含至少一个数字和一个字母' };
  }
  
  return { valid: true, message: '密码格式正确' };
}

// 验证昵称格式
function validateNickname(nickname) {
  if (!nickname) {
    return { valid: false, message: '昵称不能为空' };
  }
  
  if (nickname.length < 2) {
    return { valid: false, message: '昵称长度至少2位' };
  }
  
  if (nickname.length > 20) {
    return { valid: false, message: '昵称长度不能超过20位' };
  }
  
  // 检查是否包含特殊字符
  const specialChars = /[<>\"'&]/;
  if (specialChars.test(nickname)) {
    return { valid: false, message: '昵称不能包含特殊字符' };
  }
  
  return { valid: true, message: '昵称格式正确' };
}

// 从请求头中提取token
function extractTokenFromHeader(authHeader) {
  if (!authHeader) {
    return null;
  }
  
  const parts = authHeader.split(' ');
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return null;
  }
  
  return parts[1];
}

module.exports = {
  hashPassword,
  verifyPassword,
  generateToken,
  verifyToken,
  generateTokenHash,
  generateRandomString,
  validatePassword,
  validateNickname,
  extractTokenFromHeader
};
