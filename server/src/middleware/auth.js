const { verifyToken, extractTokenFromHeader, generateTokenHash } = require('../utils/auth');
const { get } = require('../utils/database');
const { UnauthorizedError } = require('./errorHandler');

// 认证中间件
async function authenticateToken(req, res, next) {
  try {
    const authHeader = req.headers.authorization;
    const token = extractTokenFromHeader(authHeader);
    
    if (!token) {
      throw new UnauthorizedError('缺少认证token');
    }

    // 验证token
    const decoded = verifyToken(token);
    
    // 检查token是否在数据库中存在且未过期
    const tokenHash = generateTokenHash(token);
    const session = await get(
      'SELECT * FROM user_sessions WHERE token_hash = ? AND expires_at > datetime("now")',
      [tokenHash]
    );
    
    if (!session) {
      throw new UnauthorizedError('token已失效');
    }

    // 获取用户信息
    const user = await get(
      'SELECT id, phone, nickname, avatar_url, is_verified, created_at FROM users WHERE id = ?',
      [decoded.userId]
    );
    
    if (!user) {
      throw new UnauthorizedError('用户不存在');
    }

    // 将用户信息添加到请求对象中
    req.user = user;
    req.sessionId = session.id;
    
    next();
  } catch (error) {
    if (error instanceof UnauthorizedError) {
      next(error);
    } else {
      next(new UnauthorizedError('认证失败'));
    }
  }
}

// 可选认证中间件（不强制要求认证）
async function optionalAuth(req, res, next) {
  try {
    const authHeader = req.headers.authorization;
    const token = extractTokenFromHeader(authHeader);
    
    if (token) {
      const decoded = verifyToken(token);
      const tokenHash = generateTokenHash(token);
      const session = await get(
        'SELECT * FROM user_sessions WHERE token_hash = ? AND expires_at > datetime("now")',
        [tokenHash]
      );
      
      if (session) {
        const user = await get(
          'SELECT id, phone, nickname, avatar_url, is_verified, created_at FROM users WHERE id = ?',
          [decoded.userId]
        );
        
        if (user) {
          req.user = user;
          req.sessionId = session.id;
        }
      }
    }
    
    next();
  } catch (error) {
    // 可选认证失败时不抛出错误，继续执行
    next();
  }
}

module.exports = {
  authenticateToken,
  optionalAuth
};
