# 服务器配置
PORT=3000
NODE_ENV=development

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-home2-app-2024
JWT_EXPIRES_IN=7d

# 数据库配置
DB_PATH=./database.sqlite

# 短信服务配置 (这里使用模拟短信服务，生产环境需要配置真实的短信服务商)
SMS_PROVIDER=mock
# 如果使用阿里云短信服务
# ALIYUN_ACCESS_KEY_ID=your-access-key-id
# ALIYUN_ACCESS_KEY_SECRET=your-access-key-secret
# ALIYUN_SMS_SIGN_NAME=your-sign-name
# ALIYUN_SMS_TEMPLATE_CODE=your-template-code

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
