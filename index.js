const dotenv = require('dotenv');
const { connectDB } = require('./config/db');
const { syncModels } = require('./models');
const app = require('./app');
const initServices = require('./scripts/initServices');

// 添加未捕获异常处理
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  // 记录错误但不退出进程
  console.log('服务器将继续运行...');
});

// 添加未处理的Promise拒绝处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  // 记录错误但不退出进程
  console.log('服务器将继续运行...');
});

dotenv.config();

// 端口可能会动态变化

let PORT = process.env.PORT || 5050;

// 启动服务器
const startServer = async () => {
  try {
    // 连接数据库
    await connectDB();

    // 禁用数据库同步和服务数据初始化，因为遇到了 "Too many keys specified; max 64 keys allowed" 错误
    // await syncModels();
    // await initServices();

    const server = app.listen(PORT, () => {
      console.log(`服务器运行在 ${process.env.NODE_ENV || 'development'} 模式下的端口 ${PORT}`);
    });

    // 处理服务器错误
    server.on('error', (error) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`端口 ${PORT} 已被占用，尝试使用端口 ${PORT + 1}`);
        PORT = Number(PORT) + 1;
        setTimeout(startServer, 1000);
      } else {
        console.error('服务器错误:', error);
        // 尝试重新启动服务器
        setTimeout(() => {
          console.log('尝试重新启动服务器...');
          startServer();
        }, 5000);
      }
    });

    // 优雅关闭
    process.on('SIGTERM', gracefulShutdown);
    process.on('SIGINT', gracefulShutdown);

    function gracefulShutdown() {
      console.log('正在关闭服务器...');
      server.close(() => {
        console.log('服务器已关闭');
        process.exit(0);
      });

      // 如果10秒后服务器仍未关闭，则强制退出
      setTimeout(() => {
        console.error('无法正常关闭服务器，强制退出');
        process.exit(1);
      }, 10000);
    }
  } catch (error) {
    console.error(`启动服务器错误: ${error.message}`);
    console.log('5秒后尝试重新启动服务器...');
    setTimeout(startServer, 5000);
  }
};

// 启动服务器
startServer();
