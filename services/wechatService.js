/**
 * 微信服务
 */
const axios = require('axios');
const crypto = require('crypto');
const fs = require('fs');
const xml2js = require('xml2js');
const wechatConfig = require('../config/wechat');
const { User } = require('../models');

/**
 * 微信服务类
 */
class WechatService {
  /**
   * 小程序登录
   * @param {string} code 小程序登录code
   * @returns {Promise<Object>} 登录结果
   */
  async miniProgramLogin(code) {
    try {
      // 获取openid和session_key
      const { appId, appSecret } = wechatConfig.miniProgram;
      const url = `${wechatConfig.apiUrls.code2Session}?appid=${appId}&secret=${appSecret}&js_code=${code}&grant_type=authorization_code`;
      
      const response = await axios.get(url);
      const { openid, session_key, unionid, errcode, errmsg } = response.data;
      
      if (errcode) {
        throw new Error(`微信登录失败: ${errmsg}`);
      }
      
      // 查找或创建用户
      let user = await User.findOne({ where: { wechatOpenId: openid } });
      
      if (!user) {
        // 创建新用户
        user = await User.create({
          wechatOpenId: openid,
          wechatUnionId: unionid,
          role: 'customer',
          status: 'active',
        });
      }
      
      return {
        user,
        openid,
        session_key,
        unionid,
      };
    } catch (error) {
      console.error('微信小程序登录失败:', error);
      throw error;
    }
  }
  
  /**
   * 更新微信用户信息
   * @param {string} openid 微信openid
   * @param {Object} userInfo 用户信息
   * @returns {Promise<Object>} 更新后的用户
   */
  async updateUserInfo(openid, userInfo) {
    try {
      const user = await User.findOne({ where: { wechatOpenId: openid } });
      
      if (!user) {
        throw new Error('用户不存在');
      }
      
      // 更新用户信息
      await user.update({
        name: userInfo.nickName,
        avatar: userInfo.avatarUrl,
        gender: userInfo.gender === 1 ? 'male' : (userInfo.gender === 2 ? 'female' : 'other'),
        country: userInfo.country,
        province: userInfo.province,
        city: userInfo.city,
      });
      
      return user;
    } catch (error) {
      console.error('更新微信用户信息失败:', error);
      throw error;
    }
  }
  
  /**
   * 公众号网页授权登录
   * @param {string} code 授权code
   * @returns {Promise<Object>} 登录结果
   */
  async officialAccountLogin(code) {
    try {
      // 获取access_token
      const { appId, appSecret } = wechatConfig.officialAccount;
      const url = `${wechatConfig.apiUrls.oauth2AccessToken}?appid=${appId}&secret=${appSecret}&code=${code}&grant_type=authorization_code`;
      
      const response = await axios.get(url);
      const { access_token, refresh_token, openid, unionid, errcode, errmsg } = response.data;
      
      if (errcode) {
        throw new Error(`微信公众号登录失败: ${errmsg}`);
      }
      
      // 获取用户信息
      const userInfoUrl = `${wechatConfig.apiUrls.oauth2UserInfo}?access_token=${access_token}&openid=${openid}&lang=zh_CN`;
      const userInfoResponse = await axios.get(userInfoUrl);
      const userInfo = userInfoResponse.data;
      
      if (userInfo.errcode) {
        throw new Error(`获取微信用户信息失败: ${userInfo.errmsg}`);
      }
      
      // 查找或创建用户
      let user = await User.findOne({ where: { wechatOpenId: openid } });
      
      if (!user) {
        // 创建新用户
        user = await User.create({
          wechatOpenId: openid,
          wechatUnionId: unionid,
          name: userInfo.nickname,
          avatar: userInfo.headimgurl,
          gender: userInfo.sex === 1 ? 'male' : (userInfo.sex === 2 ? 'female' : 'other'),
          country: userInfo.country,
          province: userInfo.province,
          city: userInfo.city,
          role: 'customer',
          status: 'active',
        });
      } else {
        // 更新用户信息
        await user.update({
          name: userInfo.nickname,
          avatar: userInfo.headimgurl,
          gender: userInfo.sex === 1 ? 'male' : (userInfo.sex === 2 ? 'female' : 'other'),
          country: userInfo.country,
          province: userInfo.province,
          city: userInfo.city,
        });
      }
      
      return {
        user,
        access_token,
        refresh_token,
        openid,
        unionid,
      };
    } catch (error) {
      console.error('微信公众号登录失败:', error);
      throw error;
    }
  }
}

module.exports = new WechatService();
