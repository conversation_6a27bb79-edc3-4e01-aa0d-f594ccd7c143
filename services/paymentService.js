/**
 * 支付服务
 */
const crypto = require('crypto');
const fs = require('fs');
const axios = require('axios');
const xml2js = require('xml2js');
const wechatConfig = require('../config/wechat').payment;
const { Order, Payment } = require('../models');

/**
 * 支付服务类
 */
class PaymentService {
  /**
   * 生成随机字符串
   * @param {number} length 长度
   * @returns {string} 随机字符串
   */
  generateNonceStr(length = 32) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let nonceStr = '';
    for (let i = 0; i < length; i++) {
      nonceStr += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return nonceStr;
  }
  
  /**
   * 生成签名
   * @param {Object} params 参数
   * @returns {string} 签名
   */
  generateSign(params) {
    // 按照参数名ASCII码从小到大排序
    const sortedParams = Object.keys(params).sort().reduce((result, key) => {
      if (params[key] !== '' && params[key] !== undefined && key !== 'sign') {
        result[key] = params[key];
      }
      return result;
    }, {});
    
    // 拼接字符串
    let stringA = '';
    for (const key in sortedParams) {
      stringA += `${key}=${sortedParams[key]}&`;
    }
    
    // 拼接API密钥
    const stringSignTemp = `${stringA}key=${wechatConfig.apiKey}`;
    
    // MD5加密并转为大写
    return crypto.createHash('md5').update(stringSignTemp).digest('hex').toUpperCase();
  }
  
  /**
   * 对象转XML
   * @param {Object} obj 对象
   * @returns {string} XML字符串
   */
  objectToXml(obj) {
    let xml = '<xml>';
    for (const key in obj) {
      if (obj[key] !== undefined && obj[key] !== null) {
        xml += `<${key}>${obj[key]}</${key}>`;
      }
    }
    xml += '</xml>';
    return xml;
  }
  
  /**
   * XML转对象
   * @param {string} xml XML字符串
   * @returns {Promise<Object>} 对象
   */
  async xmlToObject(xml) {
    return new Promise((resolve, reject) => {
      xml2js.parseString(xml, { explicitArray: false, trim: true }, (err, result) => {
        if (err) {
          reject(err);
        } else {
          resolve(result.xml);
        }
      });
    });
  }
  
  /**
   * 创建微信支付订单
   * @param {Object} orderData 订单数据
   * @returns {Promise<Object>} 支付参数
   */
  async createWechatPayment(orderData) {
    try {
      const { orderId, totalAmount, description, openid, tradeType = 'JSAPI', clientIp } = orderData;
      
      // 查询订单
      const order = await Order.findByPk(orderId);
      if (!order) {
        throw new Error('订单不存在');
      }
      
      // 构建请求参数
      const params = {
        appid: wechatConfig.appId,
        mch_id: wechatConfig.mchId,
        nonce_str: this.generateNonceStr(),
        body: description || '家政服务订单',
        out_trade_no: orderId.toString(),
        total_fee: Math.round(totalAmount * 100), // 转为分
        spbill_create_ip: clientIp || '127.0.0.1',
        notify_url: wechatConfig.notifyUrl,
        trade_type: tradeType,
      };
      
      // JSAPI 必须传 openid
      if (tradeType === 'JSAPI' && openid) {
        params.openid = openid;
      }
      
      // 生成签名
      params.sign = this.generateSign(params);
      
      // 转为 XML
      const xmlData = this.objectToXml(params);
      
      // 发送请求
      const response = await axios.post(wechatConfig.apiUrls.unifiedOrder, xmlData, {
        headers: { 'Content-Type': 'text/xml' },
      });
      
      // 解析响应
      const result = await this.xmlToObject(response.data);
      
      if (result.return_code !== 'SUCCESS' || result.result_code !== 'SUCCESS') {
        throw new Error(result.return_msg || result.err_code_des || '微信支付下单失败');
      }
      
      // 创建支付记录
      await Payment.create({
        orderId,
        paymentMethod: 'wechat',
        transactionId: '',
        amount: totalAmount,
        status: 'pending',
        paymentData: JSON.stringify(result),
      });
      
      // 返回支付参数
      if (tradeType === 'JSAPI') {
        // 小程序或公众号支付
        const payParams = {
          appId: wechatConfig.appId,
          timeStamp: Math.floor(Date.now() / 1000).toString(),
          nonceStr: this.generateNonceStr(),
          package: `prepay_id=${result.prepay_id}`,
          signType: 'MD5',
        };
        
        payParams.paySign = this.generateSign(payParams);
        
        return payParams;
      } else if (tradeType === 'NATIVE') {
        // 扫码支付
        return {
          codeUrl: result.code_url,
        };
      } else if (tradeType === 'APP') {
        // APP支付
        const payParams = {
          appid: wechatConfig.appId,
          partnerid: wechatConfig.mchId,
          prepayid: result.prepay_id,
          package: 'Sign=WXPay',
          noncestr: this.generateNonceStr(),
          timestamp: Math.floor(Date.now() / 1000).toString(),
        };
        
        payParams.sign = this.generateSign(payParams);
        
        return payParams;
      }
      
      return result;
    } catch (error) {
      console.error('创建微信支付订单失败:', error);
      throw error;
    }
  }
  
  /**
   * 处理微信支付通知
   * @param {string} xmlData 通知数据
   * @returns {Promise<Object>} 处理结果
   */
  async handleWechatPayNotify(xmlData) {
    try {
      // 解析通知数据
      const result = await this.xmlToObject(xmlData);
      
      if (result.return_code !== 'SUCCESS') {
        throw new Error(result.return_msg || '支付失败');
      }
      
      // 验证签名
      const sign = result.sign;
      delete result.sign;
      const calculatedSign = this.generateSign(result);
      
      if (calculatedSign !== sign) {
        throw new Error('签名验证失败');
      }
      
      // 查询支付记录
      const payment = await Payment.findOne({
        where: { orderId: result.out_trade_no },
      });
      
      if (!payment) {
        throw new Error('支付记录不存在');
      }
      
      // 更新支付记录
      await payment.update({
        transactionId: result.transaction_id,
        status: 'completed',
        paymentData: JSON.stringify(result),
      });
      
      // 更新订单状态
      const order = await Order.findByPk(result.out_trade_no);
      if (order) {
        await order.update({
          status: 'confirmed',
          paymentStatus: 'paid',
        });
      }
      
      return {
        success: true,
        message: '支付成功',
      };
    } catch (error) {
      console.error('处理微信支付通知失败:', error);
      throw error;
    }
  }
}

module.exports = new PaymentService();
