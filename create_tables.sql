-- 创建用户表
CREATE TABLE `DUser` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','manager','staff') DEFAULT 'staff',
  `phone` varchar(255) NOT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 创建服务表
CREATE TABLE `DService` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `duration` float NOT NULL DEFAULT '1',
  `category` enum('日常保洁','深度清洁','专项服务','家电清洗','其他') NOT NULL,
  `isActive` tinyint(1) DEFAULT '1',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 创建订单表
CREATE TABLE `DOrder` (
  `id` int NOT NULL AUTO_INCREMENT,
  `customerName` varchar(255) NOT NULL,
  `customerPhone` varchar(255) NOT NULL,
  `customerAddress` varchar(255) NOT NULL,
  `serviceId` int DEFAULT NULL,
  `assignedStaffId` int DEFAULT NULL,
  `scheduledDate` datetime NOT NULL,
  `status` enum('待确认','已确认','进行中','已完成','已取消') DEFAULT '待确认',
  `totalPrice` decimal(10,2) NOT NULL DEFAULT '0.00',
  `paymentStatus` enum('未支付','已支付','已退款') DEFAULT '未支付',
  `notes` text,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `serviceId` (`serviceId`),
  KEY `assignedStaffId` (`assignedStaffId`),
  CONSTRAINT `DOrder_ibfk_1` FOREIGN KEY (`serviceId`) REFERENCES `DService` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `DOrder_ibfk_2` FOREIGN KEY (`assignedStaffId`) REFERENCES `DUser` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 创建一个初始管理员用户
INSERT INTO `DUser` (`name`, `email`, `password`, `role`, `phone`, `createdAt`, `updatedAt`)
VALUES ('管理员', '<EMAIL>', '$2a$10$X7yCJJJCXwl.fQ.Aq3ZVA.4Ue7lnrH7Vd/jXTRVnLiTZOBs5EaEZm', 'admin', '13800138000', NOW(), NOW());

-- 创建一些示例服务
INSERT INTO `DService` (`name`, `description`, `price`, `duration`, `category`, `isActive`, `createdAt`, `updatedAt`)
VALUES 
('日常保洁', '提供日常家居清洁服务', 100.00, 2, '日常保洁', 1, NOW(), NOW()),
('深度清洁', '提供全面深度清洁服务', 300.00, 4, '深度清洁', 1, NOW(), NOW()),
('家电清洗', '提供专业家电清洗服务', 150.00, 3, '家电清洗', 1, NOW(), NOW());
