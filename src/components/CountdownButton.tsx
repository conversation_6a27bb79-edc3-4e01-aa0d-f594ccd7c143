import React, { useState, useEffect } from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';

interface CountdownButtonProps {
  onPress: () => Promise<boolean> | boolean;
  disabled?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  countdown?: number;
  text?: string;
  countdownText?: (seconds: number) => string;
}

export const CountdownButton: React.FC<CountdownButtonProps> = ({
  onPress,
  disabled = false,
  style,
  textStyle,
  countdown = 60,
  text = '发送验证码',
  countdownText = (seconds) => `${seconds}s后重发`,
}) => {
  const [isCountingDown, setIsCountingDown] = useState(false);
  const [remainingTime, setRemainingTime] = useState(0);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    
    if (isCountingDown && remainingTime > 0) {
      timer = setTimeout(() => {
        setRemainingTime(remainingTime - 1);
      }, 1000);
    } else if (remainingTime === 0) {
      setIsCountingDown(false);
    }

    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [isCountingDown, remainingTime]);

  const handlePress = async () => {
    if (disabled || isCountingDown) {
      return;
    }

    try {
      const result = await onPress();
      if (result !== false) {
        setIsCountingDown(true);
        setRemainingTime(countdown);
      }
    } catch (error) {
      console.error('CountdownButton onPress error:', error);
    }
  };

  const isDisabled = disabled || isCountingDown;
  const buttonText = isCountingDown ? countdownText(remainingTime) : text;

  return (
    <TouchableOpacity
      style={[
        styles.button,
        isDisabled && styles.buttonDisabled,
        style,
      ]}
      onPress={handlePress}
      disabled={isDisabled}
    >
      <Text
        style={[
          styles.buttonText,
          isDisabled && styles.buttonTextDisabled,
          textStyle,
        ]}
      >
        {buttonText}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 100,
  },
  buttonDisabled: {
    backgroundColor: '#f0f0f0',
  },
  buttonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  buttonTextDisabled: {
    color: '#999',
  },
});
