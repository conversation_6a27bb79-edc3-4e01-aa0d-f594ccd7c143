import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';
import { formatCountdown, handleApiError } from '../utils/helpers';
import apiService from '../services/api';

interface SmsCodeInputProps {
  phone: string;
  type: 'login' | 'register';
  value: string;
  onChangeText: (text: string) => void;
  error?: string;
  disabled?: boolean;
}

const SmsCodeInput: React.FC<SmsCodeInputProps> = ({
  phone,
  type,
  value,
  onChangeText,
  error,
  disabled = false,
}) => {
  const [countdown, setCountdown] = useState(0);
  const [isSending, setIsSending] = useState(false);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  const sendSmsCode = async () => {
    if (countdown > 0 || isSending || !phone) {
      return;
    }

    try {
      setIsSending(true);
      const response = await apiService.sendSms({ phone, type });
      
      if (response.success) {
        setCountdown(60); // 60秒倒计时
        Alert.alert('成功', '验证码发送成功');
      } else {
        Alert.alert('发送失败', response.message || '验证码发送失败');
      }
    } catch (error) {
      const errorMessage = handleApiError(error);
      Alert.alert('发送失败', errorMessage);
    } finally {
      setIsSending(false);
    }
  };

  const canSendSms = countdown === 0 && !isSending && phone.length === 11;

  return (
    <View style={styles.container}>
      <View style={styles.inputContainer}>
        <TextInput
          style={[styles.input, error ? styles.inputError : null]}
          placeholder="请输入6位验证码"
          value={value}
          onChangeText={onChangeText}
          keyboardType="numeric"
          maxLength={6}
          editable={!disabled}
          placeholderTextColor="#999"
        />
        <TouchableOpacity
          style={[
            styles.sendButton,
            canSendSms ? styles.sendButtonActive : styles.sendButtonDisabled,
          ]}
          onPress={sendSmsCode}
          disabled={!canSendSms}
        >
          <Text
            style={[
              styles.sendButtonText,
              canSendSms ? styles.sendButtonTextActive : styles.sendButtonTextDisabled,
            ]}
          >
            {isSending ? '发送中...' : formatCountdown(countdown)}
          </Text>
        </TouchableOpacity>
      </View>
      {error ? <Text style={styles.errorText}>{error}</Text> : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  input: {
    flex: 1,
    height: 48,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  inputError: {
    borderColor: '#ff4757',
  },
  sendButton: {
    marginLeft: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 100,
    alignItems: 'center',
  },
  sendButtonActive: {
    backgroundColor: '#007AFF',
  },
  sendButtonDisabled: {
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  sendButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  sendButtonTextActive: {
    color: '#fff',
  },
  sendButtonTextDisabled: {
    color: '#999',
  },
  errorText: {
    color: '#ff4757',
    fontSize: 12,
    marginTop: 4,
    marginLeft: 4,
  },
});

export default SmsCodeInput;
