import React, {useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
} from 'react-native';
import {useAuth} from '../contexts/AuthContext';
import {formatPhoneNumber} from '../utils/helpers';
import {AMapSdk} from 'react-native-amap3d';
import {Platform} from 'react-native';
import {MapView, MapType} from 'react-native-amap3d';

const HomeScreen: React.FC = () => {
  const {user, logout} = useAuth();

  useEffect(() => {
    AMapSdk.init(
      Platform.select({
        android: 'c52c7169e6df23490e3114330098aaac',
        ios: '186d3464209b74effa4d8391f441f14d',
      }),
    );
  }, []);
  const handleLogout = () => {
    Alert.alert('确认登出', '您确定要登出吗？', [
      {
        text: '取消',
        style: 'cancel',
      },
      {
        text: '确定',
        style: 'destructive',
        onPress: logout,
      },
    ]);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <MapView
          mapType={MapType.Standard}
          onLoad={() => console.log('onLoad')}
          onPress={({nativeEvent}) => console.log(nativeEvent)}
          onCameraIdle={({nativeEvent}) => console.log(nativeEvent)}
          initialCameraPosition={{
            target: {
              latitude: 28.15,
              longitude: 113.63,
            },
            zoom: 15,
          }}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  content: {
    flex: 1,
    padding: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  userInfo: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 24,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  userInfoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
    width: 80,
  },
  infoValue: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  verified: {
    color: '#28a745',
  },
  unverified: {
    color: '#ffc107',
  },
  actions: {
    gap: 12,
  },
  actionButton: {
    backgroundColor: '#007AFF',
    height: 48,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  logoutButton: {
    backgroundColor: '#ff4757',
    marginTop: 12,
  },
  logoutButtonText: {
    color: '#fff',
  },
});

export default HomeScreen;
