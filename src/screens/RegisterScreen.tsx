import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { FormErrors } from '../types/auth';
import { validateRegisterForm, hasFormErrors } from '../utils/validation';
import { handleApiError, getDeviceInfo } from '../utils/helpers';
import SmsCodeInput from '../components/SmsCodeInput';
import apiService from '../services/api';

interface RegisterScreenProps {
  navigation: any;
}

const RegisterScreen: React.FC<RegisterScreenProps> = ({ navigation }) => {
  const { login } = useAuth();
  
  const [phone, setPhone] = useState('');
  const [code, setCode] = useState('');
  const [nickname, setNickname] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [showPassword, setShowPassword] = useState(false);

  const handleRegister = async () => {
    if (isLoading) return;

    // 表单验证
    const formErrors = validateRegisterForm(phone, code, nickname, password);
    
    // 确认密码验证
    if (password && password !== confirmPassword) {
      formErrors.password = '两次输入的密码不一致';
    }

    if (hasFormErrors(formErrors)) {
      setErrors(formErrors);
      return;
    }

    setErrors({});
    setIsLoading(true);

    try {
      const deviceInfo = getDeviceInfo();
      const response = await apiService.register({
        phone,
        code,
        nickname,
        password: password || undefined,
        deviceInfo,
      });

      if (response.success && response.data) {
        await login(response.data);
        Alert.alert('成功', '注册成功', [
          {
            text: '确定',
            onPress: () => {
              // 导航到主页面（这里需要根据你的导航结构调整）
              // navigation.replace('Home');
            },
          },
        ]);
      } else {
        Alert.alert('注册失败', response.message || '注册失败');
      }
    } catch (error) {
      const errorMessage = handleApiError(error);
      setErrors({ general: errorMessage });
      Alert.alert('注册失败', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const goToLogin = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.header}>
            <Text style={styles.title}>创建账号</Text>
            <Text style={styles.subtitle}>请填写以下信息完成注册</Text>
          </View>

          <View style={styles.form}>
            {/* 手机号输入 */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>手机号 *</Text>
              <TextInput
                style={[styles.input, errors.phone ? styles.inputError : null]}
                placeholder="请输入手机号"
                value={phone}
                onChangeText={setPhone}
                keyboardType="phone-pad"
                maxLength={11}
                placeholderTextColor="#999"
              />
              {errors.phone ? <Text style={styles.errorText}>{errors.phone}</Text> : null}
            </View>

            {/* 验证码输入 */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>验证码 *</Text>
              <SmsCodeInput
                phone={phone}
                type="register"
                value={code}
                onChangeText={setCode}
                error={errors.code}
                disabled={isLoading}
              />
            </View>

            {/* 昵称输入 */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>昵称 *</Text>
              <TextInput
                style={[styles.input, errors.nickname ? styles.inputError : null]}
                placeholder="请输入昵称"
                value={nickname}
                onChangeText={setNickname}
                maxLength={20}
                placeholderTextColor="#999"
              />
              {errors.nickname ? <Text style={styles.errorText}>{errors.nickname}</Text> : null}
            </View>

            {/* 密码设置提示 */}
            <View style={styles.passwordTip}>
              <Text style={styles.passwordTipText}>
                密码为可选项，不设置密码只能使用验证码登录
              </Text>
            </View>

            {/* 密码输入 */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>密码（可选）</Text>
              <TextInput
                style={[styles.input, errors.password ? styles.inputError : null]}
                placeholder="请输入密码（6-50位，包含数字和字母）"
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                placeholderTextColor="#999"
              />
              {errors.password ? <Text style={styles.errorText}>{errors.password}</Text> : null}
            </View>

            {/* 确认密码输入 */}
            {password ? (
              <View style={styles.inputGroup}>
                <Text style={styles.label}>确认密码</Text>
                <TextInput
                  style={[styles.input, errors.password ? styles.inputError : null]}
                  placeholder="请再次输入密码"
                  value={confirmPassword}
                  onChangeText={setConfirmPassword}
                  secureTextEntry={!showPassword}
                  placeholderTextColor="#999"
                />
              </View>
            ) : null}

            {/* 显示密码选项 */}
            {password ? (
              <TouchableOpacity
                style={styles.showPasswordContainer}
                onPress={() => setShowPassword(!showPassword)}
              >
                <Text style={styles.showPasswordText}>
                  {showPassword ? '隐藏密码' : '显示密码'}
                </Text>
              </TouchableOpacity>
            ) : null}

            {/* 通用错误信息 */}
            {errors.general ? (
              <Text style={styles.generalErrorText}>{errors.general}</Text>
            ) : null}

            {/* 注册按钮 */}
            <TouchableOpacity
              style={[styles.registerButton, isLoading ? styles.registerButtonDisabled : null]}
              onPress={handleRegister}
              disabled={isLoading}
            >
              <Text style={styles.registerButtonText}>
                {isLoading ? '注册中...' : '注册'}
              </Text>
            </TouchableOpacity>

            {/* 登录链接 */}
            <View style={styles.loginContainer}>
              <Text style={styles.loginText}>已有账号？</Text>
              <TouchableOpacity onPress={goToLogin}>
                <Text style={styles.loginLink}>立即登录</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  form: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  inputError: {
    borderColor: '#ff4757',
  },
  errorText: {
    color: '#ff4757',
    fontSize: 12,
    marginTop: 4,
  },
  passwordTip: {
    backgroundColor: '#f0f8ff',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  passwordTipText: {
    fontSize: 12,
    color: '#007AFF',
    textAlign: 'center',
  },
  showPasswordContainer: {
    alignSelf: 'flex-end',
    marginBottom: 16,
  },
  showPasswordText: {
    fontSize: 14,
    color: '#007AFF',
  },
  generalErrorText: {
    color: '#ff4757',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 16,
  },
  registerButton: {
    backgroundColor: '#007AFF',
    height: 48,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  registerButtonDisabled: {
    backgroundColor: '#ccc',
  },
  registerButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginText: {
    fontSize: 14,
    color: '#666',
  },
  loginLink: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
    marginLeft: 4,
  },
});

export default RegisterScreen;
