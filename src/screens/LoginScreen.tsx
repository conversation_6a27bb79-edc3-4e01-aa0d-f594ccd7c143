import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { LoginType, FormErrors } from '../types/auth';
import { validateLoginForm, hasFormErrors } from '../utils/validation';
import { handleApiError, getDeviceInfo } from '../utils/helpers';
import SmsCodeInput from '../components/SmsCodeInput';
import apiService from '../services/api';

import { registerApp,sendAuthRequest } from "native-wechat";
interface LoginScreenProps {
  navigation: any;
}

const LoginScreen: React.FC<LoginScreenProps> = ({ navigation }) => {
  const { login } = useAuth();
  
  const [loginType, setLoginType] = useState<LoginType>(LoginType.SMS);
  const [phone, setPhone] = useState('');
  const [code, setCode] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});


  
  useEffect(() => {
    return registerApp({
      appid: "wx5cf395c16e836284",
    });
  }, []);

  const handleLogin = async () => {
    if (isLoading) return;

    // 表单验证
    const formErrors = validateLoginForm(
      phone,
      code,
      password,
      loginType === LoginType.PASSWORD
    );

    if (hasFormErrors(formErrors)) {
      setErrors(formErrors);
      return;
    }

    setErrors({});
    setIsLoading(true);

    try {
      const deviceInfo = getDeviceInfo();
      let response;

      if (loginType === LoginType.SMS) {
        response = await apiService.loginWithSms({
          phone,
          code,
          deviceInfo,
        });
      } else {
        response = await apiService.loginWithPassword({
          phone,
          password,
          deviceInfo,
        });
      }

      if (response.success && response.data) {
        await login(response.data);
        Alert.alert('成功', '登录成功');
        // 导航到主页面（这里需要根据你的导航结构调整）
        // navigation.replace('Home');
      } else {
        Alert.alert('登录失败', response.message || '登录失败');
      }
    } catch (error) {
      const errorMessage = handleApiError(error);
      setErrors({ general: errorMessage });
      Alert.alert('登录失败', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const switchLoginType = () => {
    setLoginType(loginType === LoginType.SMS ? LoginType.PASSWORD : LoginType.SMS);
    setErrors({});
    setCode('');
    setPassword('');
  };

  const goToRegister = async() => {

  const {
    data: { code },
  } = await sendAuthRequest();
    // navigation.navigate('register');
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.header}>
            <Text style={styles.title}>欢迎登录</Text>
            <Text style={styles.subtitle}>请输入您的手机号进行登录</Text>
          </View>

          <View style={styles.form}>
            {/* 手机号输入 */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>手机号</Text>
              <TextInput
                style={[styles.input, errors.phone ? styles.inputError : null]}
                placeholder="请输入手机号"
                value={phone}
                onChangeText={setPhone}
                keyboardType="phone-pad"
                maxLength={11}
                placeholderTextColor="#999"
              />
              {errors.phone ? <Text style={styles.errorText}>{errors.phone}</Text> : null}
            </View>

            {/* 登录方式切换 */}
            {/* <View style={styles.loginTypeContainer}>
              <TouchableOpacity
                style={[
                  styles.loginTypeButton,
                  loginType === LoginType.SMS ? styles.loginTypeButtonActive : null,
                ]}
                onPress={() => setLoginType(LoginType.SMS)}
              >
                <Text
                  style={[
                    styles.loginTypeButtonText,
                    loginType === LoginType.SMS ? styles.loginTypeButtonTextActive : null,
                  ]}
                >
                  验证码登录
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.loginTypeButton,
                  loginType === LoginType.PASSWORD ? styles.loginTypeButtonActive : null,
                ]}
                onPress={() => setLoginType(LoginType.PASSWORD)}
              >
                <Text
                  style={[
                    styles.loginTypeButtonText,
                    loginType === LoginType.PASSWORD ? styles.loginTypeButtonTextActive : null,
                  ]}
                >
                  密码登录
                </Text>
              </TouchableOpacity>
            </View> */}

            {/* 验证码或密码输入 */}
            {loginType === LoginType.SMS ? (
              <View style={styles.inputGroup}>
                <Text style={styles.label}>验证码</Text>
                <SmsCodeInput
                  phone={phone}
                  type="login"
                  value={code}
                  onChangeText={setCode}
                  error={errors.code}
                  disabled={isLoading}
                />
              </View>
            ) : (
              <View style={styles.inputGroup}>
                <Text style={styles.label}>密码</Text>
                <TextInput
                  style={[styles.input, errors.password ? styles.inputError : null]}
                  placeholder="请输入密码"
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry
                  placeholderTextColor="#999"
                />
                {errors.password ? <Text style={styles.errorText}>{errors.password}</Text> : null}
              </View>
            )}

            {/* 通用错误信息 */}
            {errors.general ? (
              <Text style={styles.generalErrorText}>{errors.general}</Text>
            ) : null}

            {/* 登录按钮 */}
            <TouchableOpacity
              style={[styles.loginButton, isLoading ? styles.loginButtonDisabled : null]}
              onPress={handleLogin}
              disabled={isLoading}
            >
              <Text style={styles.loginButtonText}>
                {isLoading ? '登录中...' : '登录'}
              </Text>
            </TouchableOpacity>

            {/* 注册链接 */}
            <View style={styles.registerContainer}>
              <Text style={styles.registerText}>还没有账号？</Text>
              <TouchableOpacity onPress={goToRegister}>
                <Text style={styles.registerLink}>立即注册</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  form: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  inputError: {
    borderColor: '#ff4757',
  },
  errorText: {
    color: '#ff4757',
    fontSize: 12,
    marginTop: 4,
  },
  loginTypeContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 4,
  },
  loginTypeButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 6,
  },
  loginTypeButtonActive: {
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  loginTypeButtonText: {
    fontSize: 14,
    color: '#666',
  },
  loginTypeButtonTextActive: {
    color: '#007AFF',
    fontWeight: '500',
  },
  generalErrorText: {
    color: '#ff4757',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 16,
  },
  loginButton: {
    backgroundColor: '#007AFF',
    height: 48,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  loginButtonDisabled: {
    backgroundColor: '#ccc',
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  registerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  registerText: {
    fontSize: 14,
    color: '#666',
  },
  registerLink: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
    marginLeft: 4,
  },
});

export default LoginScreen;
