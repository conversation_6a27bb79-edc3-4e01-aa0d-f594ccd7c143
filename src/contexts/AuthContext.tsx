import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AuthContextType, User, AuthResponse } from '../types/auth';
import apiService from '../services/api';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 初始化认证状态
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      setIsLoading(true);
      
      // 从本地存储获取token和用户信息
      const [storedToken, storedUser] = await AsyncStorage.multiGet([
        'auth_token',
        'user_info'
      ]);

      const tokenValue = storedToken[1];
      const userValue = storedUser[1];

      if (tokenValue && userValue) {
        setToken(tokenValue);
        setUser(JSON.parse(userValue));
        
        // 验证token是否仍然有效
        try {
          const response = await apiService.getProfile();
          if (response.success && response.data) {
            setUser(response.data.user);
            // 更新本地存储的用户信息
            await AsyncStorage.setItem('user_info', JSON.stringify(response.data.user));
          }
        } catch (error) {
          console.log('Token验证失败，清除认证状态');
          await logout();
        }
      }
    } catch (error) {
      console.error('初始化认证状态失败:', error);
      await logout();
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (data: AuthResponse) => {
    try {
      setIsLoading(true);
      
      // 保存到状态
      setToken(data.token);
      setUser(data.user);
      
      // 保存到本地存储
      await AsyncStorage.multiSet([
        ['auth_token', data.token],
        ['user_info', JSON.stringify(data.user)]
      ]);
    } catch (error) {
      console.error('保存认证信息失败:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      
      // 如果有token，尝试调用后端登出接口
      if (token) {
        try {
          await apiService.logout();
        } catch (error) {
          console.log('后端登出失败，继续清除本地状态');
        }
      }
      
      // 清除状态
      setToken(null);
      setUser(null);
      
      // 清除本地存储
      await AsyncStorage.multiRemove(['auth_token', 'user_info']);
    } catch (error) {
      console.error('登出失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateUser = (updatedUser: User) => {
    setUser(updatedUser);
    // 更新本地存储
    AsyncStorage.setItem('user_info', JSON.stringify(updatedUser));
  };

  const value: AuthContextType = {
    user,
    token,
    isLoading,
    login,
    logout,
    updateUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth必须在AuthProvider内部使用');
  }
  return context;
};
