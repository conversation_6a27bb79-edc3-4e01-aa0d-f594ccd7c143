import AsyncStorage from '@react-native-async-storage/async-storage';

// 模拟API基础URL，实际项目中应该从配置文件读取
const API_BASE_URL = 'http://localhost:5050/api/app';

interface LoginResponse {
  success: boolean;
  message: string;
  data: {
    id: number;
    name: string;
    phone: string;
    email?: string;
    avatar?: string;
    role: string;
    phoneVerified: boolean;
    token: string;
  };
}

interface RegisterData {
  phone: string;
  password: string;
  smsCode: string;
  nickname: string;
}

interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
}

class AuthServiceClass {
  private token: string | null = null;

  constructor() {
    this.loadToken();
  }

  // 从本地存储加载token
  private async loadToken() {
    try {
      this.token = await AsyncStorage.getItem('auth_token');
    } catch (error) {
      console.error('加载token失败:', error);
    }
  }

  // 保存token到本地存储
  private async saveToken(token: string) {
    try {
      this.token = token;
      await AsyncStorage.setItem('auth_token', token);
    } catch (error) {
      console.error('保存token失败:', error);
    }
  }

  // 清除token
  private async clearToken() {
    try {
      this.token = null;
      await AsyncStorage.removeItem('auth_token');
    } catch (error) {
      console.error('清除token失败:', error);
    }
  }

  // 通用请求方法
  private async request<T = any>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || '请求失败');
      }

      return data;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('网络请求失败');
    }
  }

  // 发送短信验证码
  async sendSmsCode(
    phone: string,
    type: 'register' | 'login' | 'reset_password' | 'bind_phone'
  ): Promise<ApiResponse> {
    return this.request('/auth/send-sms', {
      method: 'POST',
      body: JSON.stringify({ phone, type }),
    });
  }

  // 密码登录
  async loginWithPassword(phone: string, password: string): Promise<LoginResponse> {
    const response = await this.request<LoginResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ phone, password }),
    });

    if (response.data?.token) {
      await this.saveToken(response.data.token);
    }

    return response;
  }

  // 验证码登录
  async loginWithSms(phone: string, code: string): Promise<LoginResponse> {
    const response = await this.request<LoginResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ phone, code }),
    });

    if (response.data?.token) {
      await this.saveToken(response.data.token);
    }

    return response;
  }

  // 注册
  async register(data: RegisterData): Promise<ApiResponse> {
    return this.request('/auth/register', {
      method: 'POST',
      body: JSON.stringify({
        phone: data.phone,
        code: data.smsCode,
        name: data.nickname,
        password: data.password,
      }),
    });
  }

  // 重置密码
  async resetPassword(
    phone: string,
    code: string,
    newPassword: string
  ): Promise<ApiResponse> {
    return this.request('/profile/reset-password', {
      method: 'POST',
      body: JSON.stringify({
        phone,
        code,
        newPassword,
      }),
    });
  }

  // 微信登录
  async loginWithWechat(code: string, type: 'miniprogram' | 'official' = 'miniprogram'): Promise<LoginResponse> {
    const response = await this.request<LoginResponse>('/auth/wechat-login', {
      method: 'POST',
      body: JSON.stringify({ code, type }),
    });

    if (response.data?.token) {
      await this.saveToken(response.data.token);
    }

    return response;
  }

  // 获取用户信息
  async getUserProfile(): Promise<ApiResponse> {
    return this.request('/profile');
  }

  // 更新用户信息
  async updateProfile(data: {
    name?: string;
    email?: string;
    avatar?: string;
    gender?: string;
  }): Promise<ApiResponse> {
    return this.request('/profile', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // 绑定手机号
  async bindPhone(phone: string, code: string): Promise<ApiResponse> {
    return this.request('/profile/bind-phone', {
      method: 'POST',
      body: JSON.stringify({ phone, code }),
    });
  }

  // 修改密码
  async changePassword(oldPassword: string, newPassword: string): Promise<ApiResponse> {
    return this.request('/profile/password', {
      method: 'PUT',
      body: JSON.stringify({ oldPassword, newPassword }),
    });
  }

  // 登出
  async logout(): Promise<void> {
    await this.clearToken();
  }

  // 检查是否已登录
  isLoggedIn(): boolean {
    return !!this.token;
  }

  // 获取当前token
  getToken(): string | null {
    return this.token;
  }
}

export const AuthService = new AuthServiceClass();
