// @ts-ignore
/* eslint-disable */

/**
 * 模拟数据服务
 * 用于在没有后端的情况下提供模拟数据
 */

// 用户数据
export const mockUsers = [
  {
    id: '1',
    name: '管理员',
    email: '<EMAIL>',
    role: 'admin',
    phone: '13800138000',
    avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
    status: 'active',
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
  },
  {
    id: '2',
    name: '张三',
    email: 'zhang<PERSON>@example.com',
    role: 'staff',
    phone: '13800138001',
    avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
    status: 'active',
    createdAt: '2023-01-02T00:00:00Z',
    updatedAt: '2023-01-02T00:00:00Z',
  },
  {
    id: '3',
    name: '李四',
    email: '<EMAIL>',
    role: 'staff',
    phone: '13800138002',
    avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
    status: 'active',
    createdAt: '2023-01-03T00:00:00Z',
    updatedAt: '2023-01-03T00:00:00Z',
  },
];

// 服务数据
export const mockServices = [
  {
    id: '1',
    name: '日常保洁',
    description: '提供日常家居清洁服务',
    price: 100,
    duration: 120,
    category: '保洁',
    status: 'active',
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
  },
  {
    id: '2',
    name: '深度保洁',
    description: '提供深度家居清洁服务',
    price: 200,
    duration: 240,
    category: '保洁',
    status: 'active',
    createdAt: '2023-01-02T00:00:00Z',
    updatedAt: '2023-01-02T00:00:00Z',
  },
  {
    id: '3',
    name: '厨房保洁',
    description: '提供厨房专项清洁服务',
    price: 150,
    duration: 180,
    category: '保洁',
    status: 'active',
    createdAt: '2023-01-03T00:00:00Z',
    updatedAt: '2023-01-03T00:00:00Z',
  },
];

// 订单数据
export const mockOrders = [
  {
    id: '1',
    customerId: '1',
    customerName: '王五',
    serviceId: '1',
    serviceName: '日常保洁',
    staffId: '2',
    staffName: '张三',
    status: 'completed',
    price: 100,
    address: '北京市朝阳区某小区1号楼1单元101',
    scheduledAt: '2023-02-01T10:00:00Z',
    completedAt: '2023-02-01T12:00:00Z',
    createdAt: '2023-01-25T00:00:00Z',
    updatedAt: '2023-02-01T12:00:00Z',
  },
  {
    id: '2',
    customerId: '2',
    customerName: '赵六',
    serviceId: '2',
    serviceName: '深度保洁',
    staffId: '3',
    staffName: '李四',
    status: 'in_progress',
    price: 200,
    address: '北京市海淀区某小区2号楼2单元202',
    scheduledAt: '2023-02-02T14:00:00Z',
    completedAt: null,
    createdAt: '2023-01-26T00:00:00Z',
    updatedAt: '2023-02-02T14:00:00Z',
  },
  {
    id: '3',
    customerId: '3',
    customerName: '钱七',
    serviceId: '3',
    serviceName: '厨房保洁',
    staffId: '2',
    staffName: '张三',
    status: 'pending',
    price: 150,
    address: '北京市西城区某小区3号楼3单元303',
    scheduledAt: '2023-02-03T09:00:00Z',
    completedAt: null,
    createdAt: '2023-01-27T00:00:00Z',
    updatedAt: '2023-01-27T00:00:00Z',
  },
];

// 客户数据
export const mockCustomers = [
  {
    id: '1',
    name: '王五',
    phone: '13900139000',
    email: '<EMAIL>',
    address: '北京市朝阳区某小区1号楼1单元101',
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
  },
  {
    id: '2',
    name: '赵六',
    phone: '13900139001',
    email: '<EMAIL>',
    address: '北京市海淀区某小区2号楼2单元202',
    createdAt: '2023-01-02T00:00:00Z',
    updatedAt: '2023-01-02T00:00:00Z',
  },
  {
    id: '3',
    name: '钱七',
    phone: '13900139002',
    email: '<EMAIL>',
    address: '北京市西城区某小区3号楼3单元303',
    createdAt: '2023-01-03T00:00:00Z',
    updatedAt: '2023-01-03T00:00:00Z',
  },
];

// 统计数据
export const mockStats = {
  totalOrders: 100,
  totalRevenue: 15000,
  totalCustomers: 50,
  totalStaff: 10,
  ordersByStatus: {
    pending: 20,
    in_progress: 30,
    completed: 45,
    cancelled: 5,
  },
  revenueByMonth: [
    { month: '1月', revenue: 1000 },
    { month: '2月', revenue: 1200 },
    { month: '3月', revenue: 1500 },
    { month: '4月', revenue: 1300 },
    { month: '5月', revenue: 1600 },
    { month: '6月', revenue: 1800 },
  ],
  ordersByService: [
    { service: '日常保洁', count: 40 },
    { service: '深度保洁', count: 30 },
    { service: '厨房保洁', count: 30 },
  ],
};

// 评价数据
export const mockReviews = [
  {
    id: '1',
    orderId: '1',
    customerId: '1',
    customerName: '王五',
    serviceId: '1',
    serviceName: '日常保洁',
    staffId: '2',
    staffName: '张三',
    rating: 5,
    comment: '服务很好，非常满意',
    createdAt: '2023-02-01T13:00:00Z',
    updatedAt: '2023-02-01T13:00:00Z',
  },
  {
    id: '2',
    orderId: '2',
    customerId: '2',
    customerName: '赵六',
    serviceId: '2',
    serviceName: '深度保洁',
    staffId: '3',
    staffName: '李四',
    rating: 4,
    comment: '服务还不错，但有些地方可以改进',
    createdAt: '2023-02-02T16:00:00Z',
    updatedAt: '2023-02-02T16:00:00Z',
  },
];

// 排班数据
export const mockSchedules = [
  {
    id: '1',
    staffId: '2',
    staffName: '张三',
    date: '2023-02-01',
    timeSlots: [
      { start: '09:00', end: '12:00', orderId: '1' },
      { start: '14:00', end: '17:00', orderId: null },
    ],
    createdAt: '2023-01-25T00:00:00Z',
    updatedAt: '2023-01-25T00:00:00Z',
  },
  {
    id: '2',
    staffId: '3',
    staffName: '李四',
    date: '2023-02-02',
    timeSlots: [
      { start: '09:00', end: '12:00', orderId: null },
      { start: '14:00', end: '17:00', orderId: '2' },
    ],
    createdAt: '2023-01-26T00:00:00Z',
    updatedAt: '2023-01-26T00:00:00Z',
  },
];

// 财务数据
export const mockFinances = [
  {
    id: '1',
    orderId: '1',
    customerId: '1',
    customerName: '王五',
    amount: 100,
    type: 'income',
    status: 'completed',
    paymentMethod: 'wechat',
    description: '日常保洁服务费用',
    createdAt: '2023-02-01T12:00:00Z',
    updatedAt: '2023-02-01T12:00:00Z',
  },
  {
    id: '2',
    orderId: '2',
    customerId: '2',
    customerName: '赵六',
    amount: 200,
    type: 'income',
    status: 'pending',
    paymentMethod: 'alipay',
    description: '深度保洁服务费用',
    createdAt: '2023-02-02T14:00:00Z',
    updatedAt: '2023-02-02T14:00:00Z',
  },
];
