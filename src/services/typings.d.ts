declare namespace API {
  // 登录相关
  type LoginParams = {
    username?: string;
    password?: string;
    autoLogin?: boolean;
    type?: string;
  };

  type LoginResult = {
    token?: string;
    id?: string;
    name?: string;
    email?: string;
    role?: string;
    status?: string;
    type?: string;
    currentAuthority?: string;
  };

  // 用户相关
  type CurrentUser = {
    name?: string;
    avatar?: string;
    userid?: string;
    email?: string;
    phone?: string;
    role?: string;
    isActive?: boolean;
    createdAt?: string;
  };

  type UserInfo = {
    id: string;
    name: string;
    email: string;
    phone?: string;
    avatar?: string;
    role: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };

  type UserList = {
    data: UserInfo[];
    total: number;
    success: boolean;
  };

  type UserCreateParams = {
    name: string;
    email: string;
    password: string;
    phone?: string;
    avatar?: string;
    role?: string;
    isActive?: boolean;
  };

  type UserUpdateParams = {
    name?: string;
    email?: string;
    password?: string;
    phone?: string;
    avatar?: string;
    role?: string;
    isActive?: boolean;
  };

  // 服务相关
  type ServiceInfo = {
    id: string;
    name: string;
    description: string;
    price: number;
    duration: number;
    category: string;
    image?: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };

  type ServiceList = {
    data: ServiceInfo[];
    total: number;
    success: boolean;
  };

  type ServiceCreateParams = {
    name: string;
    description: string;
    price: number;
    duration: number;
    category: string;
    image?: string;
    isActive?: boolean;
  };

  type ServiceUpdateParams = {
    name?: string;
    description?: string;
    price?: number;
    duration?: number;
    category?: string;
    image?: string;
    isActive?: boolean;
  };

  // 订单相关
  type OrderInfo = {
    id: string;
    orderNumber: string;
    customerId: string;
    customerName: string;
    serviceId: string;
    serviceName: string;
    staffId?: string;
    staffName?: string;
    status: string;
    scheduledAt: string;
    address: string;
    price: number;
    paymentStatus: string;
    paymentMethod?: string;
    notes?: string;
    createdAt: string;
    updatedAt: string;
  };

  type OrderList = {
    data: OrderInfo[];
    total: number;
    success: boolean;
  };

  type OrderCreateParams = {
    customerId: string;
    serviceId: string;
    staffId?: string;
    scheduledAt: string;
    address: string;
    price: number;
    notes?: string;
  };

  type OrderUpdateParams = {
    customerId?: string;
    serviceId?: string;
    staffId?: string;
    status?: string;
    scheduledAt?: string;
    address?: string;
    price?: number;
    paymentStatus?: string;
    paymentMethod?: string;
    notes?: string;
  };

  // 客户相关
  type CustomerInfo = {
    id: string;
    name: string;
    phone: string;
    email?: string;
    address: string;
    notes?: string;
    createdAt: string;
    updatedAt: string;
  };

  type CustomerList = {
    data: CustomerInfo[];
    total: number;
    success: boolean;
  };

  type CustomerCreateParams = {
    name: string;
    phone: string;
    email?: string;
    address: string;
    notes?: string;
  };

  type CustomerUpdateParams = {
    name?: string;
    phone?: string;
    email?: string;
    address?: string;
    notes?: string;
  };

  // 评价相关
  type ReviewInfo = {
    id: string;
    orderId: string;
    customerId: string;
    customerName: string;
    serviceId: string;
    serviceName: string;
    staffId: string;
    staffName: string;
    rating: number;
    comment?: string;
    createdAt: string;
  };

  type ReviewList = {
    data: ReviewInfo[];
    total: number;
    success: boolean;
  };

  // 排班相关
  type ScheduleInfo = {
    id: string;
    staffId: string;
    staffName: string;
    date: string;
    startTime: string;
    endTime: string;
    status: string;
    notes?: string;
  };

  type ScheduleList = {
    data: ScheduleInfo[];
    total: number;
    success: boolean;
  };

  // 财务相关
  type FinanceInfo = {
    id: string;
    type: string;
    amount: number;
    description: string;
    date: string;
    orderId?: string;
    orderNumber?: string;
    status: string;
    createdAt: string;
  };

  type FinanceList = {
    data: FinanceInfo[];
    total: number;
    success: boolean;
  };

  // 统计相关
  type StatsData = {
    orderStats: {
      total: number;
      completed: number;
      pending: number;
      cancelled: number;
    };
    revenueStats: {
      total: number;
      monthly: number;
      weekly: number;
      daily: number;
    };
    serviceStats: {
      mostPopular: {
        id: string;
        name: string;
        count: number;
      }[];
      categories: {
        name: string;
        count: number;
      }[];
    };
    customerStats: {
      total: number;
      new: number;
      returning: number;
    };
    staffStats: {
      total: number;
      active: number;
      topPerformers: {
        id: string;
        name: string;
        ordersCompleted: number;
      }[];
    };
  };
}
