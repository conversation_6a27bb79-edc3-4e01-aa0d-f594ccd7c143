// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/**
 * 家政后台管理系统 API 服务
 * 用于连接到后端接口
 */

// API 基础 URL
const BASE_URL = 'http://localhost:5050/api';

// 用户相关接口
export async function login(body: API.LoginParams, options?: { [key: string]: any }) {
  console.log('Login request body:', body);
  return request<API.LoginResult>(`${BASE_URL}/auth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      email: body.username,
      password: body.password
    },
    ...(options || {}),
  });
}

export async function getUsers(options?: { [key: string]: any }) {
  console.log('Fetching users with options:', options);
  const { page, limit, name, email, phone, role, isActive, ...restOptions } = options || {};

  // 构建查询参数
  const params: Record<string, any> = {};
  if (page) params.page = page;
  if (limit) params.limit = limit;
  if (name) params.name = name;
  if (email) params.email = email;
  if (phone) params.phone = phone;
  if (role) params.role = role;
  if (isActive !== undefined) params.isActive = isActive;

  console.log('Request params:', params);

  const response = await request<any>(`${BASE_URL}/admin/users`, {
    method: 'GET',
    params,
    ...restOptions,
  });

  console.log('Users API response:', response);

  // 处理后端返回的数据格式
  if (Array.isArray(response)) {
    return {
      data: response,
      total: response.length,
      success: true,
    };
  }

  return response;
}

export async function getUserById(id: string, options?: { [key: string]: any }) {
  // 如果id是'profile'，则获取当前用户的个人资料
  const url = id === 'profile'
    ? `${BASE_URL}/users/profile`
    : `${BASE_URL}/admin/users/${id}`;

  return request<API.UserInfo>(url, {
    method: 'GET',
    ...(options || {}),
  });
}

// 修改密码
export async function changePassword(body: API.ChangePasswordParams, options?: { [key: string]: any }) {
  return request<API.OperationResult>(`${BASE_URL}/users/profile/password`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}


export async function updateUser(id: string, body: API.UserUpdateParams, options?: { [key: string]: any }) {
  return request<API.UserInfo>(`${BASE_URL}/admin/users/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function createUser(body: API.UserCreateParams, options?: { [key: string]: any }) {
  return request<API.UserInfo>(`${BASE_URL}/admin/users`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function deleteUser(id: string, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${BASE_URL}/admin/users/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

// 服务相关接口
export async function getServices(options?: { [key: string]: any }) {
  console.log('Fetching services with options:', options);
  const { page, limit, name, category, price, isActive, ...restOptions } = options || {};

  // 构建查询参数
  const params: Record<string, any> = {};
  if (page) params.page = page;
  if (limit) params.limit = limit;
  if (name) params.name = name;
  if (category) params.category = category;
  if (price) params.price = price;
  if (isActive !== undefined) params.isActive = isActive;

  console.log('Request params:', params);

  const response = await request<any>(`${BASE_URL}/admin/services`, {
    method: 'GET',
    params,
    ...restOptions,
  });

  console.log('Services API response:', response);

  // 处理后端返回的数据格式
  if (Array.isArray(response)) {
    return {
      data: response,
      total: response.length,
      success: true,
    };
  }

  return response;
}

export async function getServiceById(id: string, options?: { [key: string]: any }) {
  return request<API.ServiceInfo>(`${BASE_URL}/admin/services/${id}`, {
    method: 'GET',
    ...(options || {}),
  });
}

export async function updateService(id: string, body: API.ServiceUpdateParams, options?: { [key: string]: any }) {
  return request<API.ServiceInfo>(`${BASE_URL}/admin/services/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function createService(body: API.ServiceCreateParams, options?: { [key: string]: any }) {
  return request<API.ServiceInfo>(`${BASE_URL}/admin/services`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function deleteService(id: string, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${BASE_URL}/admin/services/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

// 订单相关接口
export async function getOrders(options?: { [key: string]: any }) {
  console.log('Fetching orders with options:', options);
  const { page, limit, customerName, status, paymentStatus, ...restOptions } = options || {};

  // 构建查询参数
  const params: Record<string, any> = {};
  if (page) params.page = page;
  if (limit) params.limit = limit;
  if (customerName) params.customerName = customerName;
  if (status) params.status = status;
  if (paymentStatus) params.paymentStatus = paymentStatus;

  console.log('Request params:', params);

  const response = await request<any>(`${BASE_URL}/admin/orders`, {
    method: 'GET',
    params,
    ...restOptions,
  });

  console.log('Orders API response:', response);

  // 处理后端返回的数据格式
  if (Array.isArray(response)) {
    return {
      data: response,
      total: response.length,
      success: true,
    };
  }

  return response;
}

export async function getOrderById(id: string, options?: { [key: string]: any }) {
  return request<API.OrderInfo>(`${BASE_URL}/admin/orders/${id}`, {
    method: 'GET',
    ...(options || {}),
  });
}

export async function updateOrder(id: string, body: API.OrderUpdateParams, options?: { [key: string]: any }) {
  return request<API.OrderInfo>(`${BASE_URL}/admin/orders/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function createOrder(body: API.OrderCreateParams, options?: { [key: string]: any }) {
  return request<API.OrderInfo>(`${BASE_URL}/admin/orders`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function deleteOrder(id: string, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${BASE_URL}/admin/orders/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

// 客户相关接口
export async function getCustomers(options?: { [key: string]: any }) {
  console.log('Fetching customers...');
  const response = await request<any>(`${BASE_URL}/admin/customers`, {
    method: 'GET',
    ...(options || {}),
  });

  console.log('Customers API response:', response);

  // 处理后端返回的数据格式
  if (Array.isArray(response)) {
    return {
      data: response,
      total: response.length,
      success: true,
    };
  }

  return response;
}

export async function getCustomerById(id: string, options?: { [key: string]: any }) {
  return request<API.CustomerInfo>(`${BASE_URL}/admin/customers/${id}`, {
    method: 'GET',
    ...(options || {}),
  });
}

export async function updateCustomer(id: string, body: API.CustomerUpdateParams, options?: { [key: string]: any }) {
  return request<API.CustomerInfo>(`${BASE_URL}/admin/customers/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function createCustomer(body: API.CustomerCreateParams, options?: { [key: string]: any }) {
  return request<API.CustomerInfo>(`${BASE_URL}/admin/customers`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function deleteCustomer(id: string, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${BASE_URL}/admin/customers/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

// 统计相关接口
export async function getStats(options?: { [key: string]: any }) {
  console.log('Fetching stats...');
  try {
    const response = await request<any>(`${BASE_URL}/admin/stats`, {
      method: 'GET',
      ...(options || {}),
    });

    console.log('Stats API response:', response);

    // 将后端返回的数据转换为前端需要的格式
    return {
      data: {
        orderStats: {
          total: response.totalOrders || 0,
          completed: response.completedOrders || 0,
          pending: response.pendingOrders || 0,
          cancelled: 0, // 后端暂时没有提供这个数据
        },
        revenueStats: {
          total: response.monthlyIncome || 0, // 后端暂时只提供月收入
          monthly: response.monthlyIncome || 0,
          weekly: Math.round((response.monthlyIncome || 0) / 4), // 估算周收入
          daily: Math.round((response.monthlyIncome || 0) / 30), // 估算日收入
        },
        serviceStats: {
          mostPopular: [
            {
              id: '1',
              name: '日常家居保洁',
              count: 45,
            },
            {
              id: '2',
              name: '深度清洁',
              count: 30,
            },
          ],
          categories: [
            {
              name: '日常保洁',
              count: 50,
            },
            {
              name: '深度清洁',
              count: 30,
            },
          ],
        },
        customerStats: {
          total: response.totalCustomers || 0,
          new: Math.round((response.totalCustomers || 0) * 0.2), // 估算新客户数
          returning: Math.round((response.totalCustomers || 0) * 0.8), // 估算回头客数
        },
        staffStats: {
          total: response.totalStaff || 0,
          active: response.totalStaff || 0,
          topPerformers: [
            {
              id: '1',
              name: '张三',
              ordersCompleted: 25,
            },
            {
              id: '2',
              name: '李四',
              ordersCompleted: 20,
            },
          ],
        },
      },
      success: true,
    };
  } catch (error) {
    console.error('获取统计数据失败:', error);
    throw error;
  }
}

// 评价相关接口
export async function getReviews(options?: { [key: string]: any }) {
  console.log('Fetching reviews...');
  const response = await request<any>(`${BASE_URL}/admin/reviews`, {
    method: 'GET',
    ...(options || {}),
  });

  console.log('Reviews API response:', response);

  // 处理后端返回的数据格式
  if (Array.isArray(response)) {
    return {
      data: response,
      total: response.length,
      success: true,
    };
  }

  return response;
}

// 排班相关接口
export async function getSchedules(options?: { [key: string]: any }) {
  return request<API.ScheduleList>(`${BASE_URL}/admin/schedules`, {
    method: 'GET',
    ...(options || {}),
  });
}

// 财务相关接口
export async function getFinances(options?: { [key: string]: any }) {
  return request<API.FinanceList>(`${BASE_URL}/admin/finances`, {
    method: 'GET',
    ...(options || {}),
  });
}
