import axios, { AxiosInstance, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  ApiResponse, 
  AuthResponse, 
  LoginSmsRequest, 
  LoginPasswordRequest, 
  RegisterRequest, 
  SendSmsRequest,
  User 
} from '../types/auth';

// API基础配置
const API_BASE_URL = __DEV__ 
  ? 'http://10.0.2.2:3000/api'  // Android模拟器
  : 'https://your-production-api.com/api';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 请求拦截器 - 添加认证token
    this.api.interceptors.request.use(
      async (config) => {
        const token = await AsyncStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器 - 处理通用错误
    this.api.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        return response;
      },
      async (error) => {
        if (error.response?.status === 401) {
          // token过期，清除本地存储
          await AsyncStorage.multiRemove(['auth_token', 'user_info']);
        }
        return Promise.reject(error);
      }
    );
  }

  // 发送短信验证码
  async sendSms(data: SendSmsRequest): Promise<ApiResponse> {
    const response = await this.api.post('/auth/send-sms', data);
    return response.data;
  }

  // 验证码登录
  async loginWithSms(data: LoginSmsRequest): Promise<ApiResponse<AuthResponse>> {
    const response = await this.api.post('/auth/login-sms', data);
    return response.data;
  }

  // 密码登录
  async loginWithPassword(data: LoginPasswordRequest): Promise<ApiResponse<AuthResponse>> {
    const response = await this.api.post('/auth/login-password', data);
    return response.data;
  }

  // 注册
  async register(data: RegisterRequest): Promise<ApiResponse<AuthResponse>> {
    const response = await this.api.post('/auth/register', data);
    return response.data;
  }

  // 获取用户信息
  async getProfile(): Promise<ApiResponse<{ user: User }>> {
    const response = await this.api.get('/auth/profile');
    return response.data;
  }

  // 更新用户信息
  async updateProfile(data: { nickname?: string; avatar_url?: string }): Promise<ApiResponse<{ user: User }>> {
    const response = await this.api.put('/auth/profile', data);
    return response.data;
  }

  // 设置密码
  async setPassword(data: { password: string; code: string }): Promise<ApiResponse> {
    const response = await this.api.post('/auth/set-password', data);
    return response.data;
  }

  // 登出
  async logout(): Promise<ApiResponse> {
    const response = await this.api.post('/auth/logout');
    return response.data;
  }

  // 登出所有设备
  async logoutAll(): Promise<ApiResponse> {
    const response = await this.api.post('/auth/logout-all');
    return response.data;
  }

  // 健康检查
  async healthCheck(): Promise<ApiResponse> {
    const response = await this.api.get('/health');
    return response.data;
  }
}

export const apiService = new ApiService();
export default apiService;
