declare namespace API {
  // 通用响应格式
  type Response<T> = {
    success: boolean;
    data: T;
    message?: string;
  };

  // 分页参数
  type PageParams = {
    current?: number;
    pageSize?: number;
  };

  // 登录参数
  type LoginParams = {
    email: string;
    password: string;
    autoLogin?: boolean;
  };

  // 登录结果
  type LoginResult = {
    token: string;
    user: UserInfo;
  };

  // 用户信息
  type UserInfo = {
    id: string;
    name: string;
    email: string;
    phone?: string;
    avatar?: string;
    role: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };

  // 用户列表
  type UserList = {
    data: UserInfo[];
    total: number;
    success: boolean;
  };

  // 用户创建参数
  type UserCreateParams = {
    name: string;
    email: string;
    password: string;
    phone?: string;
    role: string;
    isActive?: boolean;
  };

  // 用户更新参数
  type UserUpdateParams = {
    name?: string;
    email?: string;
    password?: string;
    phone?: string;
    role?: string;
    isActive?: boolean;
  };

  // 修改密码参数
  type ChangePasswordParams = {
    oldPassword: string;
    newPassword: string;
    confirmPassword: string;
  };

  // 操作结果
  type OperationResult = {
    success: boolean;
    message: string;
  };

  // 服务信息
  type ServiceInfo = {
    id: string;
    name: string;
    description: string;
    price: number;
    duration: number;
    category: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };

  // 服务列表
  type ServiceList = {
    data: ServiceInfo[];
    total: number;
    success: boolean;
  };

  // 服务创建参数
  type ServiceCreateParams = {
    name: string;
    description: string;
    price: number;
    duration: number;
    category: string;
    isActive?: boolean;
  };

  // 服务更新参数
  type ServiceUpdateParams = {
    name?: string;
    description?: string;
    price?: number;
    duration?: number;
    category?: string;
    isActive?: boolean;
  };

  // 订单信息
  type OrderInfo = {
    id: string;
    orderNumber: string;
    customerId: string;
    customerName: string;
    serviceId: string;
    serviceName: string;
    staffId?: string;
    staffName?: string;
    status: string;
    scheduledAt: string;
    address: string;
    price: number;
    paymentStatus: string;
    paymentMethod?: string;
    notes?: string;
    createdAt: string;
    updatedAt: string;
  };

  // 订单列表
  type OrderList = {
    data: OrderInfo[];
    total: number;
    success: boolean;
  };

  // 订单创建参数
  type OrderCreateParams = {
    customerId: string;
    serviceId: string;
    staffId?: string;
    scheduledAt: string;
    address: string;
    price: number;
    status?: string;
    paymentStatus?: string;
    paymentMethod?: string;
    notes?: string;
  };

  // 订单更新参数
  type OrderUpdateParams = {
    customerId?: string;
    serviceId?: string;
    staffId?: string;
    scheduledAt?: string;
    address?: string;
    price?: number;
    status?: string;
    paymentStatus?: string;
    paymentMethod?: string;
    notes?: string;
  };

  // 客户信息
  type CustomerInfo = {
    id: string;
    name: string;
    phone: string;
    email?: string;
    address: string;
    notes?: string;
    createdAt: string;
    updatedAt: string;
  };

  // 客户列表
  type CustomerList = {
    data: CustomerInfo[];
    total: number;
    success: boolean;
  };

  // 客户创建参数
  type CustomerCreateParams = {
    name: string;
    phone: string;
    email?: string;
    address: string;
    notes?: string;
  };

  // 客户更新参数
  type CustomerUpdateParams = {
    name?: string;
    phone?: string;
    email?: string;
    address?: string;
    notes?: string;
  };

  // 评价信息
  type ReviewInfo = {
    id: string;
    orderId: string;
    customerId: string;
    customerName: string;
    serviceId: string;
    serviceName: string;
    staffId?: string;
    staffName?: string;
    rating: number;
    comment?: string;
    createdAt: string;
  };

  // 评价列表
  type ReviewList = {
    data: ReviewInfo[];
    total: number;
    success: boolean;
  };

  // 排班信息
  type ScheduleInfo = {
    id: string;
    staffId: string;
    staffName: string;
    date: string;
    startTime: string;
    endTime: string;
    status: string;
    notes?: string;
  };

  // 排班列表
  type ScheduleList = {
    data: ScheduleInfo[];
    total: number;
    success: boolean;
  };

  // 财务信息
  type FinanceInfo = {
    id: string;
    orderId?: string;
    orderNumber?: string;
    type: string;
    amount: number;
    description: string;
    date: string;
    status: string;
    createdAt: string;
    updatedAt: string;
  };

  // 财务列表
  type FinanceList = {
    data: FinanceInfo[];
    total: number;
    success: boolean;
  };

  // 统计数据
  type StatsData = {
    orderStats: {
      total: number;
      completed: number;
      pending: number;
      cancelled: number;
    };
    revenueStats: {
      total: number;
      monthly: number;
      weekly: number;
      daily: number;
    };
    serviceStats: {
      mostPopular: {
        id: string;
        name: string;
        count: number;
      }[];
      categories: {
        name: string;
        count: number;
      }[];
    };
    customerStats: {
      total: number;
      new: number;
      returning: number;
    };
    staffStats: {
      total: number;
      active: number;
      topPerformers: {
        id: string;
        name: string;
        ordersCompleted: number;
      }[];
    };
  };
}
