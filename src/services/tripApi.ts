import Taro from '@tarojs/taro';
import { ApiResponse, Trip, Order, SearchParams, PublishTripParams, User, Chat, Message } from '../types/trip';

const BASE_URL = 'https://api.baicai-trip.com/v1';

// 请求拦截器
const request = async <T = any>(
  url: string,
  options: Taro.request.Option = {}
): Promise<ApiResponse<T>> => {
  const token = Taro.getStorageSync('token');
  
  const defaultOptions: Taro.request.Option = {
    url: `${BASE_URL}${url}`,
    method: 'GET',
    header: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.header,
    },
    ...options,
  };

  try {
    const response = await Taro.request(defaultOptions);
    
    if (response.statusCode === 200) {
      return response.data as ApiResponse<T>;
    } else {
      throw new Error(`请求失败: ${response.statusCode}`);
    }
  } catch (error) {
    console.error('API请求错误:', error);
    throw error;
  }
};

// 认证相关API
export const authApi = {
  // 发送验证码
  sendSmsCode: (phone: string, type: 'login' | 'register') =>
    request('/auth/sms-code', {
      method: 'POST',
      data: { phone, type },
    }),

  // 验证码登录
  loginWithSms: (phone: string, code: string) =>
    request<{ user: User; token: string }>('/auth/login/sms', {
      method: 'POST',
      data: { phone, code },
    }),

  // 密码登录
  loginWithPassword: (phone: string, password: string) =>
    request<{ user: User; token: string }>('/auth/login/password', {
      method: 'POST',
      data: { phone, password },
    }),

  // 注册
  register: (phone: string, code: string, password: string, name: string) =>
    request<{ user: User; token: string }>('/auth/register', {
      method: 'POST',
      data: { phone, code, password, name },
    }),

  // 微信登录
  loginWithWechat: (code: string) =>
    request<{ user: User; token: string }>('/auth/wechat', {
      method: 'POST',
      data: { code },
    }),

  // 获取用户信息
  getUserInfo: () => request<User>('/auth/user'),

  // 更新用户信息
  updateUserInfo: (data: Partial<User>) =>
    request<User>('/auth/user', {
      method: 'PUT',
      data,
    }),
};

// 行程相关API
export const tripApi = {
  // 搜索行程
  searchTrips: (params: SearchParams) =>
    request<Trip[]>('/trips/search', {
      method: 'POST',
      data: params,
    }),

  // 发布行程
  publishTrip: (params: PublishTripParams) =>
    request<Trip>('/trips', {
      method: 'POST',
      data: params,
    }),

  // 获取行程详情
  getTripDetail: (tripId: string) =>
    request<Trip>(`/trips/${tripId}`),

  // 获取我的行程
  getMyTrips: (type?: 'driver' | 'passenger', status?: string) =>
    request<Trip[]>('/trips/my', {
      data: { type, status },
    }),

  // 取消行程
  cancelTrip: (tripId: string, reason?: string) =>
    request(`/trips/${tripId}/cancel`, {
      method: 'POST',
      data: { reason },
    }),

  // 更新行程
  updateTrip: (tripId: string, data: Partial<Trip>) =>
    request<Trip>(`/trips/${tripId}`, {
      method: 'PUT',
      data,
    }),
};

// 订单相关API
export const orderApi = {
  // 创建订单
  createOrder: (tripId: string, seats: number, pickupLocation?: any, dropoffLocation?: any) =>
    request<Order>('/orders', {
      method: 'POST',
      data: { tripId, seats, pickupLocation, dropoffLocation },
    }),

  // 获取订单列表
  getOrders: (status?: string) =>
    request<Order[]>('/orders', {
      data: { status },
    }),

  // 获取订单详情
  getOrderDetail: (orderId: string) =>
    request<Order>(`/orders/${orderId}`),

  // 确认订单
  confirmOrder: (orderId: string) =>
    request(`/orders/${orderId}/confirm`, {
      method: 'POST',
    }),

  // 取消订单
  cancelOrder: (orderId: string, reason?: string) =>
    request(`/orders/${orderId}/cancel`, {
      method: 'POST',
      data: { reason },
    }),

  // 完成订单
  completeOrder: (orderId: string) =>
    request(`/orders/${orderId}/complete`, {
      method: 'POST',
    }),
};

// 聊天相关API
export const chatApi = {
  // 获取聊天列表
  getChatList: () => request<Chat[]>('/chats'),

  // 获取聊天详情
  getChatDetail: (chatId: string) => request<Chat>(`/chats/${chatId}`),

  // 发送消息
  sendMessage: (chatId: string, content: string, type: 'text' | 'image' | 'location' = 'text') =>
    request<Message>(`/chats/${chatId}/messages`, {
      method: 'POST',
      data: { content, type },
    }),

  // 获取消息列表
  getMessages: (chatId: string, page: number = 1, limit: number = 20) =>
    request<Message[]>(`/chats/${chatId}/messages`, {
      data: { page, limit },
    }),

  // 标记消息已读
  markAsRead: (chatId: string) =>
    request(`/chats/${chatId}/read`, {
      method: 'POST',
    }),
};

// 地图相关API
export const mapApi = {
  // 地址搜索
  searchAddress: (keyword: string, city?: string) =>
    request<any[]>('/map/search', {
      data: { keyword, city },
    }),

  // 逆地理编码
  reverseGeocode: (latitude: number, longitude: number) =>
    request<any>('/map/reverse-geocode', {
      data: { latitude, longitude },
    }),

  // 路线规划
  getRoute: (start: any, end: any, mode: 'driving' | 'walking' | 'transit' = 'driving') =>
    request<any>('/map/route', {
      data: { start, end, mode },
    }),

  // 距离计算
  calculateDistance: (start: any, end: any) =>
    request<{ distance: number; duration: number }>('/map/distance', {
      data: { start, end },
    }),
};

// 支付相关API
export const paymentApi = {
  // 创建支付订单
  createPayment: (orderId: string, method: 'wechat' | 'alipay') =>
    request<any>('/payments', {
      method: 'POST',
      data: { orderId, method },
    }),

  // 查询支付状态
  getPaymentStatus: (paymentId: string) =>
    request<any>(`/payments/${paymentId}/status`),
};

// 文件上传API
export const uploadApi = {
  // 上传图片
  uploadImage: async (filePath: string): Promise<string> => {
    const token = Taro.getStorageSync('token');
    
    return new Promise((resolve, reject) => {
      Taro.uploadFile({
        url: `${BASE_URL}/upload/image`,
        filePath,
        name: 'file',
        header: {
          ...(token && { Authorization: `Bearer ${token}` }),
        },
        success: (res) => {
          const data = JSON.parse(res.data);
          if (data.success) {
            resolve(data.data.url);
          } else {
            reject(new Error(data.message));
          }
        },
        fail: reject,
      });
    });
  },
};
