// 行程相关类型定义

export interface Location {
  latitude: number;
  longitude: number;
  address: string;
  name?: string;
  city?: string;
  district?: string;
}

export interface User {
  id: string;
  name: string;
  avatar?: string;
  phone: string;
  gender?: 'male' | 'female';
  age?: number;
  rating?: number;
  verified?: boolean;
  carInfo?: CarInfo;
}

export interface CarInfo {
  brand: string;
  model: string;
  color: string;
  plateNumber: string;
  seats: number;
}

export interface Trip {
  id: string;
  type: 'driver' | 'passenger';
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  driver?: User;
  passengers?: User[];
  startLocation: Location;
  endLocation: Location;
  departureTime: string;
  arrivalTime?: string;
  price: number;
  seats: number;
  availableSeats: number;
  description?: string;
  requirements?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface Order {
  id: string;
  tripId: string;
  trip: Trip;
  passenger: User;
  driver: User;
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  seats: number;
  totalPrice: number;
  pickupLocation?: Location;
  dropoffLocation?: Location;
  createdAt: string;
  updatedAt: string;
}

export interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  type: 'text' | 'image' | 'location';
  timestamp: string;
  read: boolean;
}

export interface Chat {
  id: string;
  participants: User[];
  lastMessage?: Message;
  unreadCount: number;
  tripId?: string;
  createdAt: string;
  updatedAt: string;
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  code?: number;
}

// 搜索参数
export interface SearchParams {
  startLocation: Location;
  endLocation: Location;
  departureDate: string;
  seats?: number;
  type: 'driver' | 'passenger';
}

// 发布行程参数
export interface PublishTripParams {
  type: 'driver' | 'passenger';
  startLocation: Location;
  endLocation: Location;
  departureTime: string;
  seats: number;
  price: number;
  description?: string;
  requirements?: string[];
  carInfo?: CarInfo;
}

// 地图相关
export interface MapMarker {
  id: string;
  latitude: number;
  longitude: number;
  iconPath: string;
  width?: number;
  height?: number;
  title?: string;
  callout?: {
    content: string;
    display: 'ALWAYS' | 'BYCLICK';
  };
}

// 路线规划
export interface Route {
  distance: number; // 距离（米）
  duration: number; // 时间（秒）
  polyline: string; // 路线坐标点
  steps: RouteStep[];
}

export interface RouteStep {
  instruction: string;
  distance: number;
  duration: number;
  polyline: string;
}

// 筛选条件
export interface FilterOptions {
  priceRange?: [number, number];
  departureTimeRange?: [string, string];
  gender?: 'male' | 'female';
  verified?: boolean;
  rating?: number;
  carBrand?: string[];
}

// 统计数据
export interface Statistics {
  totalTrips: number;
  completedTrips: number;
  rating: number;
  totalDistance: number;
  carbonSaved: number;
}

// 通知类型
export interface Notification {
  id: string;
  type: 'order' | 'trip' | 'system' | 'message';
  title: string;
  content: string;
  data?: any;
  read: boolean;
  createdAt: string;
}

// 支付相关
export interface Payment {
  id: string;
  orderId: string;
  amount: number;
  method: 'wechat' | 'alipay' | 'balance';
  status: 'pending' | 'success' | 'failed';
  createdAt: string;
}

// 评价
export interface Review {
  id: string;
  orderId: string;
  reviewerId: string;
  revieweeId: string;
  rating: number;
  comment?: string;
  tags?: string[];
  createdAt: string;
}

// 举报
export interface Report {
  id: string;
  reporterId: string;
  reportedId: string;
  type: 'user' | 'trip' | 'order';
  reason: string;
  description?: string;
  evidence?: string[];
  status: 'pending' | 'processing' | 'resolved' | 'rejected';
  createdAt: string;
}
