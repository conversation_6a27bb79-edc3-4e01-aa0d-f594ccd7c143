// 用户信息类型
export interface User {
  id: number;
  phone: string;
  nickname: string;
  avatar_url?: string;
  is_verified: boolean;
  created_at: string;
}

// 登录请求类型
export interface LoginSmsRequest {
  phone: string;
  code: string;
  deviceInfo?: string;
}

export interface LoginPasswordRequest {
  phone: string;
  password: string;
  deviceInfo?: string;
}

// 注册请求类型
export interface RegisterRequest {
  phone: string;
  code: string;
  password?: string;
  nickname: string;
  deviceInfo?: string;
}

// 发送短信请求类型
export interface SendSmsRequest {
  phone: string;
  type: 'login' | 'register';
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: boolean;
  code?: string;
  timestamp?: string;
}

// 认证响应类型
export interface AuthResponse {
  token: string;
  user: User;
}

// 用户上下文类型
export interface AuthContextType {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  login: (data: AuthResponse) => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (user: User) => void;
}

// 表单验证错误类型
export interface FormErrors {
  phone?: string;
  code?: string;
  password?: string;
  nickname?: string;
  general?: string;
}

// 登录方式枚举
export enum LoginType {
  SMS = 'sms',
  PASSWORD = 'password'
}
