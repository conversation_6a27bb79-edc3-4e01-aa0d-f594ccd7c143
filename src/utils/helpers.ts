import { Platform } from 'react-native';

// 获取设备信息
export const getDeviceInfo = (): string => {
  return `${Platform.OS} ${Platform.Version}`;
};

// 格式化手机号显示
export const formatPhoneNumber = (phone: string): string => {
  if (!phone || phone.length !== 11) {
    return phone;
  }
  
  return `${phone.slice(0, 3)} ${phone.slice(3, 7)} ${phone.slice(7)}`;
};

// 隐藏手机号中间部分
export const maskPhoneNumber = (phone: string): string => {
  if (!phone || phone.length !== 11) {
    return phone;
  }
  
  return `${phone.slice(0, 3)}****${phone.slice(7)}`;
};

// 倒计时格式化
export const formatCountdown = (seconds: number): string => {
  if (seconds <= 0) {
    return '获取验证码';
  }
  
  return `${seconds}s后重新获取`;
};

// 处理API错误
export const handleApiError = (error: any): string => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  
  if (error.message) {
    return error.message;
  }
  
  return '网络错误，请稍后重试';
};

// 延迟函数
export const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};
