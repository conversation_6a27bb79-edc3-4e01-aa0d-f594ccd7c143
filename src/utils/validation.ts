/**
 * 表单验证工具函数
 */

// 验证手机号
export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

// 验证密码
export const validatePassword = (password: string): boolean => {
  return password.length >= 6;
};

// 验证邮箱
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// 验证验证码
export const validateSmsCode = (code: string): boolean => {
  return /^\d{4,6}$/.test(code);
};

// 验证昵称
export const validateNickname = (nickname: string): boolean => {
  const trimmed = nickname.trim();
  return trimmed.length >= 2 && trimmed.length <= 20;
};

// 验证身份证号
export const validateIdCard = (idCard: string): boolean => {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  return idCardRegex.test(idCard);
};

// 验证中文姓名
export const validateChineseName = (name: string): boolean => {
  const chineseNameRegex = /^[\u4e00-\u9fa5]{2,10}$/;
  return chineseNameRegex.test(name);
};

// 验证银行卡号
export const validateBankCard = (cardNumber: string): boolean => {
  const bankCardRegex = /^\d{16,19}$/;
  return bankCardRegex.test(cardNumber);
};

// 验证车牌号
export const validateLicensePlate = (plate: string): boolean => {
  const plateRegex = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/;
  return plateRegex.test(plate);
};

// 验证网址
export const validateUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// 验证IP地址
export const validateIP = (ip: string): boolean => {
  const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  return ipRegex.test(ip);
};

// 验证QQ号
export const validateQQ = (qq: string): boolean => {
  const qqRegex = /^[1-9][0-9]{4,10}$/;
  return qqRegex.test(qq);
};

// 验证微信号
export const validateWechat = (wechat: string): boolean => {
  const wechatRegex = /^[a-zA-Z][-_a-zA-Z0-9]{5,19}$/;
  return wechatRegex.test(wechat);
};

// 验证邮政编码
export const validatePostalCode = (code: string): boolean => {
  const postalCodeRegex = /^[1-9]\d{5}$/;
  return postalCodeRegex.test(code);
};

// 验证数字
export const validateNumber = (value: string): boolean => {
  return !isNaN(Number(value)) && isFinite(Number(value));
};

// 验证正整数
export const validatePositiveInteger = (value: string): boolean => {
  const positiveIntegerRegex = /^[1-9]\d*$/;
  return positiveIntegerRegex.test(value);
};

// 验证小数
export const validateDecimal = (value: string, decimalPlaces: number = 2): boolean => {
  const decimalRegex = new RegExp(`^\\d+(\\.\\d{1,${decimalPlaces}})?$`);
  return decimalRegex.test(value);
};

// 验证字符串长度
export const validateLength = (
  value: string,
  minLength: number,
  maxLength?: number
): boolean => {
  if (value.length < minLength) {
    return false;
  }
  if (maxLength !== undefined && value.length > maxLength) {
    return false;
  }
  return true;
};

// 验证是否为空
export const validateRequired = (value: string): boolean => {
  return value.trim().length > 0;
};

// 验证年龄
export const validateAge = (age: string): boolean => {
  const ageNum = Number(age);
  return ageNum >= 0 && ageNum <= 150;
};

// 验证日期格式 (YYYY-MM-DD)
export const validateDate = (date: string): boolean => {
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(date)) {
    return false;
  }
  const dateObj = new Date(date);
  return dateObj instanceof Date && !isNaN(dateObj.getTime());
};

// 验证时间格式 (HH:MM)
export const validateTime = (time: string): boolean => {
  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
  return timeRegex.test(time);
};

// 组合验证器
export const createValidator = (
  validators: Array<(value: string) => boolean>
) => {
  return (value: string): boolean => {
    return validators.every(validator => validator(value));
  };
};

// 异步验证器（用于检查用户名是否已存在等）
export const createAsyncValidator = (
  asyncCheck: (value: string) => Promise<boolean>
) => {
  return async (value: string): Promise<boolean> => {
    try {
      return await asyncCheck(value);
    } catch {
      return false;
    }
  };
};
