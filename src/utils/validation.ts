import { FormErrors } from '../types/auth';

// 验证手机号
export const validatePhone = (phone: string): string | undefined => {
  if (!phone) {
    return '手机号不能为空';
  }
  
  if (!/^1[3-9]\d{9}$/.test(phone)) {
    return '请输入正确的手机号';
  }
  
  return undefined;
};

// 验证验证码
export const validateSmsCode = (code: string): string | undefined => {
  if (!code) {
    return '验证码不能为空';
  }
  
  if (!/^\d{6}$/.test(code)) {
    return '验证码必须是6位数字';
  }
  
  return undefined;
};

// 验证密码
export const validatePassword = (password: string): string | undefined => {
  if (!password) {
    return '密码不能为空';
  }
  
  if (password.length < 6) {
    return '密码长度至少6位';
  }
  
  if (password.length > 50) {
    return '密码长度不能超过50位';
  }
  
  // 检查是否包含至少一个数字和一个字母
  const hasNumber = /\d/.test(password);
  const hasLetter = /[a-zA-Z]/.test(password);
  
  if (!hasNumber || !hasLetter) {
    return '密码必须包含至少一个数字和一个字母';
  }
  
  return undefined;
};

// 验证昵称
export const validateNickname = (nickname: string): string | undefined => {
  if (!nickname) {
    return '昵称不能为空';
  }
  
  if (nickname.length < 2) {
    return '昵称长度至少2位';
  }
  
  if (nickname.length > 20) {
    return '昵称长度不能超过20位';
  }
  
  // 检查是否包含特殊字符
  const specialChars = /[<>"'&]/;
  if (specialChars.test(nickname)) {
    return '昵称不能包含特殊字符';
  }
  
  return undefined;
};

// 验证登录表单
export const validateLoginForm = (
  phone: string,
  code?: string,
  password?: string,
  isPasswordLogin: boolean = false
): FormErrors => {
  const errors: FormErrors = {};
  
  const phoneError = validatePhone(phone);
  if (phoneError) {
    errors.phone = phoneError;
  }
  
  if (isPasswordLogin) {
    if (!password) {
      errors.password = '密码不能为空';
    }
  } else {
    const codeError = validateSmsCode(code || '');
    if (codeError) {
      errors.code = codeError;
    }
  }
  
  return errors;
};

// 验证注册表单
export const validateRegisterForm = (
  phone: string,
  code: string,
  nickname: string,
  password?: string
): FormErrors => {
  const errors: FormErrors = {};
  
  const phoneError = validatePhone(phone);
  if (phoneError) {
    errors.phone = phoneError;
  }
  
  const codeError = validateSmsCode(code);
  if (codeError) {
    errors.code = codeError;
  }
  
  const nicknameError = validateNickname(nickname);
  if (nicknameError) {
    errors.nickname = nicknameError;
  }
  
  if (password) {
    const passwordError = validatePassword(password);
    if (passwordError) {
      errors.password = passwordError;
    }
  }
  
  return errors;
};

// 检查表单是否有错误
export const hasFormErrors = (errors: FormErrors): boolean => {
  return Object.keys(errors).length > 0;
};
