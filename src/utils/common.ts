import Taro from '@tarojs/taro';
import dayjs from 'dayjs';

// 格式化时间
export const formatTime = (time: string | Date, format: string = 'YYYY-MM-DD HH:mm') => {
  return dayjs(time).format(format);
};

// 计算时间差
export const getTimeDiff = (time: string | Date) => {
  const now = dayjs();
  const target = dayjs(time);
  const diff = target.diff(now, 'minute');
  
  if (diff < 0) {
    return '已过期';
  } else if (diff < 60) {
    return `${diff}分钟后`;
  } else if (diff < 1440) {
    return `${Math.floor(diff / 60)}小时后`;
  } else {
    return `${Math.floor(diff / 1440)}天后`;
  }
};

// 格式化距离
export const formatDistance = (distance: number) => {
  if (distance < 1000) {
    return `${Math.round(distance)}m`;
  } else {
    return `${(distance / 1000).toFixed(1)}km`;
  }
};

// 格式化价格
export const formatPrice = (price: number) => {
  return `¥${price.toFixed(2)}`;
};

// 手机号验证
export const validatePhone = (phone: string) => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

// 身份证验证
export const validateIdCard = (idCard: string) => {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  return idCardRegex.test(idCard);
};

// 车牌号验证
export const validatePlateNumber = (plateNumber: string) => {
  const plateRegex = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/;
  return plateRegex.test(plateNumber);
};

// 获取当前位置
export const getCurrentLocation = (): Promise<{
  latitude: number;
  longitude: number;
  address?: string;
}> => {
  return new Promise((resolve, reject) => {
    Taro.getLocation({
      type: 'gcj02',
      success: (res) => {
        resolve({
          latitude: res.latitude,
          longitude: res.longitude,
        });
      },
      fail: reject,
    });
  });
};

// 选择位置
export const chooseLocation = (): Promise<{
  name: string;
  address: string;
  latitude: number;
  longitude: number;
}> => {
  return new Promise((resolve, reject) => {
    Taro.chooseLocation({
      success: resolve,
      fail: reject,
    });
  });
};

// 打开地图导航
export const openMapNavigation = (
  latitude: number,
  longitude: number,
  name?: string,
  address?: string
) => {
  Taro.openLocation({
    latitude,
    longitude,
    name: name || '目的地',
    address: address || '',
    scale: 18,
  });
};

// 拨打电话
export const makePhoneCall = (phoneNumber: string) => {
  Taro.makePhoneCall({
    phoneNumber,
  });
};

// 复制到剪贴板
export const copyToClipboard = (data: string, showToast: boolean = true) => {
  Taro.setClipboardData({
    data,
    success: () => {
      if (showToast) {
        Taro.showToast({
          title: '已复制到剪贴板',
          icon: 'success',
        });
      }
    },
  });
};

// 显示Toast
export const showToast = (title: string, icon: 'success' | 'error' | 'loading' | 'none' = 'none') => {
  Taro.showToast({
    title,
    icon,
    duration: 2000,
  });
};

// 显示Loading
export const showLoading = (title: string = '加载中...') => {
  Taro.showLoading({
    title,
    mask: true,
  });
};

// 隐藏Loading
export const hideLoading = () => {
  Taro.hideLoading();
};

// 显示确认对话框
export const showConfirm = (
  content: string,
  title: string = '提示'
): Promise<boolean> => {
  return new Promise((resolve) => {
    Taro.showModal({
      title,
      content,
      success: (res) => {
        resolve(res.confirm);
      },
    });
  });
};

// 预览图片
export const previewImage = (urls: string[], current?: string) => {
  Taro.previewImage({
    urls,
    current: current || urls[0],
  });
};

// 选择图片
export const chooseImage = (count: number = 1): Promise<string[]> => {
  return new Promise((resolve, reject) => {
    Taro.chooseImage({
      count,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        resolve(res.tempFilePaths);
      },
      fail: reject,
    });
  });
};

// 保存图片到相册
export const saveImageToPhotosAlbum = (filePath: string) => {
  Taro.saveImageToPhotosAlbum({
    filePath,
    success: () => {
      showToast('保存成功', 'success');
    },
    fail: () => {
      showToast('保存失败', 'error');
    },
  });
};

// 获取系统信息
export const getSystemInfo = () => {
  return Taro.getSystemInfoSync();
};

// 获取网络状态
export const getNetworkType = (): Promise<string> => {
  return new Promise((resolve, reject) => {
    Taro.getNetworkType({
      success: (res) => {
        resolve(res.networkType);
      },
      fail: reject,
    });
  });
};

// 震动反馈
export const vibrateShort = () => {
  Taro.vibrateShort();
};

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let lastTime = 0;
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastTime >= wait) {
      lastTime = now;
      func(...args);
    }
  };
};

// 生成唯一ID
export const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// 深拷贝
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as any;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as any;
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {} as any;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
  
  return obj;
};

// 数组去重
export const uniqueArray = <T>(arr: T[], key?: keyof T): T[] => {
  if (!key) {
    return [...new Set(arr)];
  }
  
  const seen = new Set();
  return arr.filter(item => {
    const value = item[key];
    if (seen.has(value)) {
      return false;
    }
    seen.add(value);
    return true;
  });
};

// 计算两点间距离（米）
export const calculateDistance = (
  lat1: number,
  lng1: number,
  lat2: number,
  lng2: number
): number => {
  const R = 6371000; // 地球半径（米）
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};
