/**
 * 请求拦截器
 * 用于在请求中添加 token
 */
import { message, notification } from 'antd';
import { history } from '@umijs/max';

// 错误处理
const codeMessage: Record<number, string> = {
  200: '服务器成功返回请求的数据。',
  201: '新建或修改数据成功。',
  202: '一个请求已经进入后台排队（异步任务）。',
  204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
};

// 错误处理方案
const errorHandler = (error: any) => {
  const { response } = error;
  console.log('Error handler called:', error);

  if (response && response.status) {
    const errorText = codeMessage[response.status] || response.statusText;
    const { status, url } = response;
    console.log('Error response:', { status, url, errorText });

    // 处理 401 未授权错误
    if (status === 401) {
      console.log('401 Unauthorized error detected');
      message.error('登录已过期，请重新登录');
      localStorage.removeItem('token');
      // 不要立即跳转，给用户一些时间看到错误信息
      setTimeout(() => {
        history.push('/user/login');
      }, 1500);
      return;
    }

    notification.error({
      message: `请求错误 ${status}: ${url}`,
      description: errorText,
    });
  } else if (!response) {
    console.log('Network error detected');
    notification.error({
      description: '您的网络发生异常，无法连接服务器',
      message: '网络异常',
    });
  }

  return response;
};

// 请求拦截器
const requestInterceptor = (url: string, options: any) => {
  const token = localStorage.getItem('token');
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
    console.log('Adding token to request:', token);
    console.log('Request URL:', url);
    console.log('Request options:', JSON.stringify({
      method: options.method,
      headers: headers,
    }));
  } else {
    console.log('No token found in localStorage');
    console.log('Request URL:', url);
  }

  return {
    url,
    options: { ...options, headers },
  };
};

// 响应拦截器
const responseInterceptor = async (response: any) => {
  console.log('Response interceptor called:', response);

  // 检查响应是否是标准的 Response 对象
  if (response && typeof response.clone === 'function') {
    try {
      // 克隆响应对象
      const res = response.clone();
      console.log('Response status:', response.status);
      console.log('Response URL:', response.url);

      // 尝试解析响应
      const data = await res.json();
      console.log('Response data:', data);

      // 处理业务错误
      if (data && data.code && data.code !== 200) {
        console.log('Business error detected:', data.code, data.message);
        message.error(data.message || '请求失败');
      }
    } catch (error) {
      // 解析错误，忽略
      console.log('Response parse error:', error);
    }
  } else {
    // 如果不是标准的 Response 对象，直接返回
    console.log('Non-standard response type:', typeof response);
    console.log('Non-standard response:', response);
  }

  return response;
};

export { requestInterceptor, responseInterceptor, errorHandler };
