import { AvatarDropdown, AvatarName, Footer } from '@/components';
import { getUserById } from '@/services/api';
import type { Settings as LayoutSettings } from '@ant-design/pro-components';
import { SettingDrawer } from '@ant-design/pro-components';
import type { RunTimeLayoutConfig } from '@umijs/max';
import { history } from '@umijs/max';
import React from 'react';
import defaultSettings from '../config/defaultSettings';
import { errorConfig } from './requestErrorConfig';
import { requestInterceptor, responseInterceptor, errorHandler } from './utils/request';

const isDev = process.env.NODE_ENV === 'development';
const loginPath = '/user/login';

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
  currentUser?: API.CurrentUser;
  loading?: boolean;
  fetchUserInfo?: () => Promise<API.CurrentUser | undefined>;
}> {
  const fetchUserInfo = async () => {
    try {
      // 从 localStorage 获取 token
      const token = localStorage.getItem('token');

      if (!token) {
        history.push(loginPath);
        return undefined;
      }

      // 检查是否有缓存的用户信息
      const cachedUserInfo = localStorage.getItem('userInfo');
      if (cachedUserInfo) {
        try {
          const userInfo = JSON.parse(cachedUserInfo);
          console.log('使用缓存的用户信息:', userInfo);
          return userInfo;
        } catch (e) {
          console.error('解析缓存的用户信息失败:', e);
          // 如果解析失败，继续尝试从API获取
        }
      }

      // 获取用户信息
      try {
        // 先尝试获取用户个人资料
        const response = await getUserById('profile');

        if (response && response.id) {
          const userInfo = {
            name: response.name,
            avatar: response.avatar || 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
            userid: response.id,
            email: response.email,
            phone: response.phone,
            role: response.role,
          };

          // 缓存用户信息
          localStorage.setItem('userInfo', JSON.stringify(userInfo));

          return userInfo;
        }
      } catch (profileError) {
        console.error('获取用户资料失败:', profileError);

        // 如果获取个人资料失败，尝试使用保存的用户ID获取用户信息
        const userId = localStorage.getItem('userId');
        if (userId) {
          try {
            const userResponse = await getUserById(userId);

            if (userResponse && userResponse.id) {
              const userInfo = {
                name: userResponse.name,
                avatar: userResponse.avatar || 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
                userid: userResponse.id,
                email: userResponse.email,
                phone: userResponse.phone,
                role: userResponse.role,
              };

              // 缓存用户信息
              localStorage.setItem('userInfo', JSON.stringify(userInfo));

              return userInfo;
            }
          } catch (userError) {
            console.error('获取用户信息失败:', userError);
          }
        }
      }

      // 如果获取用户信息失败，但有token，创建一个默认用户
      // 这样可以避免重定向到登录页面
      const defaultUser = {
        name: '用户',
        avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
        userid: 'default',
        email: '',
        role: 'user',
      };

      // 缓存默认用户信息
      localStorage.setItem('userInfo', JSON.stringify(defaultUser));

      return defaultUser;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      // 不要清除token，让用户继续使用
      return {
        name: '用户',
        avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
        userid: 'default',
        email: '',
        role: 'user',
      };
    }
  };
  // 检查是否有缓存的用户信息
  const cachedUserInfo = localStorage.getItem('userInfo');
  if (cachedUserInfo) {
    try {
      const userInfo = JSON.parse(cachedUserInfo);
      console.log('使用缓存的用户信息:', userInfo);
      return {
        fetchUserInfo,
        currentUser: userInfo,
        settings: defaultSettings as Partial<LayoutSettings>,
      };
    } catch (e) {
      console.error('解析缓存的用户信息失败:', e);
    }
  }

  // 如果没有缓存的用户信息，并且不是登录页面，则获取用户信息
  const { location } = history;
  if (location.pathname !== loginPath) {
    const currentUser = await fetchUserInfo();
    return {
      fetchUserInfo,
      currentUser,
      settings: defaultSettings as Partial<LayoutSettings>,
    };
  }

  // 如果是登录页面，不获取用户信息
  return {
    fetchUserInfo,
    settings: defaultSettings as Partial<LayoutSettings>,
  };
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState, setInitialState }) => {
  return {
    actionsRender: () => [],
    avatarProps: {
      src: initialState?.currentUser?.avatar,
      title: <AvatarName />,
      render: (_, avatarChildren) => {
        return <AvatarDropdown>{avatarChildren}</AvatarDropdown>;
      },
    },
    waterMarkProps: {
      // content: initialState?.currentUser?.name,
    },
    footerRender: () => <Footer />,
    onPageChange: () => {
      const { location } = history;
      // 检查是否有token
      const token = localStorage.getItem('token');

      // 如果没有token且不是登录页面，重定向到登录页
      if (!token && location.pathname !== loginPath) {
        console.log('没有token，重定向到登录页');
        history.push(loginPath);
        return;
      }

      // 如果有token但没有currentUser，尝试从localStorage获取用户信息
      if (token && !initialState?.currentUser && location.pathname !== loginPath) {
        const cachedUserInfo = localStorage.getItem('userInfo');
        if (cachedUserInfo) {
          try {
            const userInfo = JSON.parse(cachedUserInfo);
            console.log('页面切换时使用缓存的用户信息:', userInfo);
            // 设置用户信息
            setInitialState((s) => ({
              ...s,
              currentUser: userInfo,
            }));
            return;
          } catch (e) {
            console.error('解析缓存的用户信息失败:', e);
          }
        }

        // 如果没有缓存的用户信息，重定向到登录页
        console.log('没有用户信息，重定向到登录页');
        history.push(loginPath);
      }
    },
    bgLayoutImgList: [
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/D2LWSqNny4sAAAAAAAAAAAAAFl94AQBr',
        left: 85,
        bottom: 100,
        height: '303px',
      },
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/C2TWRpJpiC0AAAAAAAAAAAAAFl94AQBr',
        bottom: -68,
        right: -45,
        height: '303px',
      },
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/F6vSTbj8KpYAAAAAAAAAAAAAFl94AQBr',
        bottom: 0,
        left: 0,
        width: '331px',
      },
    ],
    // links: isDev
    //   ? [
    //       <Link key="openapi" to="/umi/plugin/openapi" target="_blank">
    //         <LinkOutlined />
    //         <span>OpenAPI 文档</span>
    //       </Link>,
    //     ]
    //   : [],
    menuHeaderRender: undefined,
    // 自定义 403 页面
    // unAccessible: <div>unAccessible</div>,
    // 增加一个 loading 的状态
    childrenRender: (children) => {
      // if (initialState?.loading) return <PageLoading />;
      return (
        <>
          {children}
          {isDev && (
            <SettingDrawer
              disableUrlParams
              enableDarkTheme
              settings={initialState?.settings}
              onSettingChange={(settings) => {
                setInitialState((preInitialState) => ({
                  ...preInitialState,
                  settings,
                }));
              }}
            />
          )}
        </>
      );
    },
    ...initialState?.settings,
  };
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request = {
  ...errorConfig,
  requestInterceptors: [requestInterceptor],
  responseInterceptors: [responseInterceptor],
  errorHandler,
};
