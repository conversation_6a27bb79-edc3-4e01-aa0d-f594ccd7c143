import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  FooterToolbar,
  PageContainer,
  ProDescriptions,
  ProDescriptionsItemProps,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Drawer, message, Modal, Tag } from 'antd';
import React, { useRef, useState } from 'react';
import { getSchedules } from '@/services/api';
import ScheduleCalendar from './components/ScheduleCalendar';

/**
 * 排班管理页面
 */
const ScheduleList: React.FC = () => {
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [showCalendar, setShowCalendar] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.ScheduleInfo>();
  const [selectedRowsState, setSelectedRows] = useState<API.ScheduleInfo[]>([]);

  const columns: ProDescriptionsItemProps<API.ScheduleInfo>[] = [
    {
      title: '员工',
      dataIndex: 'staffName',
      render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow(entity);
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '日期',
      dataIndex: 'date',
      valueType: 'date',
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      valueType: 'time',
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      valueType: 'time',
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: {
        'scheduled': { text: '已排班', status: 'processing' },
        'working': { text: '工作中', status: 'processing' },
        'completed': { text: '已完成', status: 'success' },
        'cancelled': { text: '已取消', status: 'error' },
      },
      render: (_, record) => {
        let color = 'default';
        let text = '未知状态';
        
        switch (record.status) {
          case 'scheduled':
            color = 'blue';
            text = '已排班';
            break;
          case 'working':
            color = 'geekblue';
            text = '工作中';
            break;
          case 'completed':
            color = 'green';
            text = '已完成';
            break;
          case 'cancelled':
            color = 'red';
            text = '已取消';
            break;
        }
        
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '备注',
      dataIndex: 'notes',
      ellipsis: true,
      hideInSearch: true,
    },
  ];

  return (
    <PageContainer
      extra={[
        <Button
          key="calendar"
          type="primary"
          onClick={() => {
            setShowCalendar(true);
          }}
        >
          日历视图
        </Button>,
      ]}
    >
      <ProTable<API.ScheduleInfo>
        headerTitle="排班列表"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              message.info('请使用日历视图添加排班');
            }}
          >
            <PlusOutlined /> 新建
          </Button>,
        ]}
        request={async (params) => {
          try {
            const response = await getSchedules({
              ...params,
            });
            return {
              data: response.data || [],
              success: true,
              total: response.total || 0,
            };
          } catch (error) {
            message.error('获取排班列表失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />

      {/* 排班详情抽屉 */}
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.staffName && (
          <ProDescriptions<API.ScheduleInfo>
            column={2}
            title={`${currentRow?.staffName}的排班`}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<API.ScheduleInfo>[]}
          />
        )}
      </Drawer>

      {/* 日历视图 */}
      <ScheduleCalendar
        visible={showCalendar}
        onClose={() => {
          setShowCalendar(false);
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />
    </PageContainer>
  );
};

export default ScheduleList;
