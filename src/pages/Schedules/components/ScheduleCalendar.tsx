import React, { useEffect, useState } from 'react';
import { Calendar, Modal, Form, Select, TimePicker, Input, Button, message, Drawer, Tag } from 'antd';
import type { Moment } from 'moment';
import moment from 'moment';
import { getSchedules, getUsers } from '@/services/api';

interface ScheduleCalendarProps {
  visible: boolean;
  onClose: () => void;
}

/**
 * 排班日历组件
 */
const ScheduleCalendar: React.FC<ScheduleCalendarProps> = ({ visible, onClose }) => {
  const [form] = Form.useForm();
  const [schedules, setSchedules] = useState<API.ScheduleInfo[]>([]);
  const [staff, setStaff] = useState<{ label: string; value: string }[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedDate, setSelectedDate] = useState<Moment | null>(null);
  const [modalVisible, setModalVisible] = useState<boolean>(false);

  // 获取排班和员工数据
  useEffect(() => {
    if (visible) {
      fetchData();
    }
  }, [visible]);

  const fetchData = async () => {
    setLoading(true);
    try {
      // 获取排班数据
      const schedulesResponse = await getSchedules();
      if (schedulesResponse.data) {
        setSchedules(schedulesResponse.data);
      }

      // 获取员工数据
      const staffResponse = await getUsers();
      if (staffResponse.data) {
        setStaff(
          staffResponse.data
            .filter((user) => user.role === 'staff')
            .map((user) => ({
              label: user.name,
              value: user.id,
            })),
        );
      }
    } catch (error) {
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 日期单元格渲染
  const dateCellRender = (value: Moment) => {
    const dateStr = value.format('YYYY-MM-DD');
    const dateSchedules = schedules.filter((schedule) => schedule.date === dateStr);

    return (
      <ul className="events">
        {dateSchedules.map((schedule) => (
          <li key={schedule.id}>
            <Tag color={getStatusColor(schedule.status)}>
              {schedule.staffName}: {schedule.startTime}-{schedule.endTime}
            </Tag>
          </li>
        ))}
      </ul>
    );
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'blue';
      case 'working':
        return 'geekblue';
      case 'completed':
        return 'green';
      case 'cancelled':
        return 'red';
      default:
        return 'default';
    }
  };

  // 日期选择处理
  const handleSelect = (date: Moment) => {
    setSelectedDate(date);
    setModalVisible(true);
    form.setFieldsValue({
      date: date.format('YYYY-MM-DD'),
      startTime: moment('09:00:00', 'HH:mm:ss'),
      endTime: moment('18:00:00', 'HH:mm:ss'),
      status: 'scheduled',
    });
  };

  // 添加排班
  const handleAddSchedule = async () => {
    try {
      const values = await form.validateFields();
      
      // 模拟添加排班
      const newSchedule = {
        id: `temp-${Date.now()}`,
        staffId: values.staffId,
        staffName: staff.find((s) => s.value === values.staffId)?.label || '',
        date: values.date,
        startTime: values.startTime.format('HH:mm:ss'),
        endTime: values.endTime.format('HH:mm:ss'),
        status: values.status,
        notes: values.notes,
      };
      
      setSchedules([...schedules, newSchedule as API.ScheduleInfo]);
      message.success('排班添加成功');
      setModalVisible(false);
    } catch (error) {
      message.error('表单验证失败');
    }
  };

  return (
    <Drawer
      title="排班日历"
      width={1000}
      open={visible}
      onClose={onClose}
      bodyStyle={{ padding: 0 }}
    >
      <div style={{ padding: '24px' }}>
        <Calendar dateCellRender={dateCellRender} onSelect={handleSelect} />
      </div>

      {/* 添加排班表单 */}
      <Modal
        title="添加排班"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setModalVisible(false)}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={handleAddSchedule}>
            添加
          </Button>,
        ]}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="staffId"
            label="员工"
            rules={[{ required: true, message: '请选择员工' }]}
          >
            <Select
              placeholder="请选择员工"
              options={staff}
              loading={loading}
            />
          </Form.Item>
          <Form.Item
            name="date"
            label="日期"
            rules={[{ required: true, message: '请选择日期' }]}
          >
            <Input disabled />
          </Form.Item>
          <Form.Item
            name="startTime"
            label="开始时间"
            rules={[{ required: true, message: '请选择开始时间' }]}
          >
            <TimePicker format="HH:mm" style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="endTime"
            label="结束时间"
            rules={[{ required: true, message: '请选择结束时间' }]}
          >
            <TimePicker format="HH:mm" style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select
              placeholder="请选择状态"
              options={[
                { label: '已排班', value: 'scheduled' },
                { label: '工作中', value: 'working' },
                { label: '已完成', value: 'completed' },
                { label: '已取消', value: 'cancelled' },
              ]}
            />
          </Form.Item>
          <Form.Item name="notes" label="备注">
            <Input.TextArea rows={4} placeholder="请输入备注" />
          </Form.Item>
        </Form>
      </Modal>
    </Drawer>
  );
};

export default ScheduleCalendar;
