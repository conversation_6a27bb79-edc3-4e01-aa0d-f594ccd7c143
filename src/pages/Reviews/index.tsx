import {
  PageContainer,
  ProDescriptions,
  ProDescriptionsItemProps,
  ProTable,
} from '@ant-design/pro-components';
import { Drawer, message, Rate } from 'antd';
import React, { useRef, useState } from 'react';
import { getReviews } from '@/services/api';
import type { ActionType } from '@ant-design/pro-components';

/**
 * 评价管理页面
 */
const ReviewList: React.FC = () => {
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.ReviewInfo>();

  const columns: ProDescriptionsItemProps<API.ReviewInfo>[] = [
    {
      title: '订单ID',
      dataIndex: 'orderId',
      hideInSearch: true,
    },
    {
      title: '客户',
      dataIndex: 'customerName',
      render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow(entity);
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '服务',
      dataIndex: 'serviceName',
    },
    {
      title: '员工',
      dataIndex: 'staffName',
    },
    {
      title: '评分',
      dataIndex: 'rating',
      hideInForm: true,
      render: (_, record) => <Rate disabled defaultValue={record.rating} />,
    },
    {
      title: '评价内容',
      dataIndex: 'comment',
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '评价时间',
      dataIndex: 'createdAt',
      valueType: 'dateTime',
      hideInForm: true,
      hideInSearch: true,
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.ReviewInfo>
        headerTitle="评价列表"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        request={async (params) => {
          try {
            console.log('Requesting reviews with params:', params);
            const response = await getReviews({
              ...params,
            });
            console.log('Reviews response in component:', response);

            // 如果后端返回的是数组，则需要转换为 ProTable 需要的格式
            if (Array.isArray(response)) {
              return {
                data: response,
                success: true,
                total: response.length,
              };
            }

            // 如果后端已经返回了正确的格式
            if (response.data) {
              return {
                data: response.data,
                success: response.success,
                total: response.total,
              };
            }

            return {
              data: [],
              success: true,
              total: 0,
            };
          } catch (error) {
            console.error('获取评价列表失败:', error);
            message.error('获取评价列表失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        columns={columns}
      />

      {/* 评价详情抽屉 */}
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions<API.ReviewInfo>
            column={2}
            title="评价详情"
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<API.ReviewInfo>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default ReviewList;
