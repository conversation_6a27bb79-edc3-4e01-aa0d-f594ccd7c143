import { <PERSON><PERSON>ontainer } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Card, Col, Row, Statistic, Typography, Spin, Alert } from 'antd';
import React, { useEffect, useState } from 'react';
import { getStats } from '@/services/api';
import {
  DashboardOutlined,
  ShoppingOutlined,
  UserOutlined,
  TeamOutlined,
  DollarOutlined,
} from '@ant-design/icons';

const { Title, Paragraph } = Typography;

/**
 * 仪表盘页面
 */
const Dashboard: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const [loading, setLoading] = useState<boolean>(true);
  const [stats, setStats] = useState<API.StatsData | null>(null);
  const [error, setError] = useState<string | null>(null);

  // 获取统计数据
  useEffect(() => {
    const fetchStats = async () => {
      setLoading(true);
      try {
        const response = await getStats();
        setStats(response.data);
        setError(null);
      } catch (err) {
        console.error('获取统计数据失败:', err);
        setError('获取统计数据失败，请稍后再试');
        // 使用模拟数据
        setStats({
          orderStats: {
            total: 120,
            completed: 85,
            pending: 30,
            cancelled: 5,
          },
          revenueStats: {
            total: 25000,
            monthly: 8500,
            weekly: 2100,
            daily: 300,
          },
          serviceStats: {
            mostPopular: [
              {
                id: '1',
                name: '日常家居保洁',
                count: 45,
              },
              {
                id: '2',
                name: '深度清洁',
                count: 30,
              },
            ],
            categories: [
              {
                name: '日常保洁',
                count: 50,
              },
              {
                name: '深度清洁',
                count: 30,
              },
            ],
          },
          customerStats: {
            total: 80,
            new: 15,
            returning: 65,
          },
          staffStats: {
            total: 12,
            active: 10,
            topPerformers: [
              {
                id: '1',
                name: '张三',
                ordersCompleted: 25,
              },
              {
                id: '2',
                name: '李四',
                ordersCompleted: 20,
              },
            ],
          },
        });
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  return (
    <PageContainer>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>欢迎回来，{initialState?.currentUser?.name || '管理员'}</Title>
        <Paragraph>这里是家政后台管理系统的仪表盘，您可以查看系统的各项统计数据。</Paragraph>
      </div>

      {error && (
        <Alert
          message="数据加载错误"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}

      <Spin spinning={loading}>
        {stats && (
          <>
            {/* 订单统计 */}
            <Row gutter={24} style={{ marginBottom: 24 }}>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="总订单数"
                    value={stats.orderStats.total}
                    prefix={<DashboardOutlined />}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="已完成订单"
                    value={stats.orderStats.completed}
                    valueStyle={{ color: '#3f8600' }}
                    prefix={<DashboardOutlined />}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="待处理订单"
                    value={stats.orderStats.pending}
                    valueStyle={{ color: '#faad14' }}
                    prefix={<DashboardOutlined />}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="已取消订单"
                    value={stats.orderStats.cancelled}
                    valueStyle={{ color: '#cf1322' }}
                    prefix={<DashboardOutlined />}
                  />
                </Card>
              </Col>
            </Row>

            {/* 收入统计 */}
            <Row gutter={24} style={{ marginBottom: 24 }}>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="总收入"
                    value={stats.revenueStats.total}
                    precision={2}
                    prefix={<DollarOutlined />}
                    suffix="元"
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="本月收入"
                    value={stats.revenueStats.monthly}
                    precision={2}
                    prefix={<DollarOutlined />}
                    suffix="元"
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="本周收入"
                    value={stats.revenueStats.weekly}
                    precision={2}
                    prefix={<DollarOutlined />}
                    suffix="元"
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="今日收入"
                    value={stats.revenueStats.daily}
                    precision={2}
                    prefix={<DollarOutlined />}
                    suffix="元"
                  />
                </Card>
              </Col>
            </Row>

            {/* 客户和员工统计 */}
            <Row gutter={24}>
              <Col span={8}>
                <Card title="客户统计">
                  <Statistic
                    title="总客户数"
                    value={stats.customerStats.total}
                    prefix={<UserOutlined />}
                    style={{ marginBottom: 16 }}
                  />
                  <Statistic
                    title="新客户"
                    value={stats.customerStats.new}
                    prefix={<UserOutlined />}
                    style={{ marginBottom: 16 }}
                  />
                  <Statistic
                    title="回头客"
                    value={stats.customerStats.returning}
                    prefix={<UserOutlined />}
                  />
                </Card>
              </Col>
              <Col span={8}>
                <Card title="服务统计">
                  <div style={{ marginBottom: 16 }}>
                    <Title level={4}>最受欢迎的服务</Title>
                    {stats.serviceStats.mostPopular.map((service) => (
                      <div key={service.id} style={{ marginBottom: 8 }}>
                        <ShoppingOutlined style={{ marginRight: 8 }} />
                        {service.name}: {service.count} 次
                      </div>
                    ))}
                  </div>
                  <div>
                    <Title level={4}>服务类别</Title>
                    {stats.serviceStats.categories.map((category, index) => (
                      <div key={index} style={{ marginBottom: 8 }}>
                        <ShoppingOutlined style={{ marginRight: 8 }} />
                        {category.name}: {category.count} 次
                      </div>
                    ))}
                  </div>
                </Card>
              </Col>
              <Col span={8}>
                <Card title="员工统计">
                  <Statistic
                    title="总员工数"
                    value={stats.staffStats.total}
                    prefix={<TeamOutlined />}
                    style={{ marginBottom: 16 }}
                  />
                  <Statistic
                    title="活跃员工"
                    value={stats.staffStats.active}
                    prefix={<TeamOutlined />}
                    style={{ marginBottom: 16 }}
                  />
                  <div>
                    <Title level={4}>绩效最佳员工</Title>
                    {stats.staffStats.topPerformers.map((staff) => (
                      <div key={staff.id} style={{ marginBottom: 8 }}>
                        <TeamOutlined style={{ marginRight: 8 }} />
                        {staff.name}: 完成 {staff.ordersCompleted} 单
                      </div>
                    ))}
                  </div>
                </Card>
              </Col>
            </Row>
          </>
        )}
      </Spin>
    </PageContainer>
  );
};

export default Dashboard;
