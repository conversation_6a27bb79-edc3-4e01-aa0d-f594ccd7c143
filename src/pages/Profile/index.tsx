import React, { useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { Card, Descriptions, Button, Avatar, message, Tabs, Form, Input, Upload } from 'antd';
import { UserOutlined, UploadOutlined, LockOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import type { UploadProps } from 'antd';
import { changePassword } from '@/services/api';

const { TabPane } = Tabs;

/**
 * 个人资料页面
 */
const Profile: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const [loading, setLoading] = useState<boolean>(false);
  const [passwordForm] = Form.useForm();
  const [profileForm] = Form.useForm();

  // 更新个人资料
  const handleUpdateProfile = async (values: any) => {
    setLoading(true);
    try {
      // 模拟更新个人资料
      message.success('个人资料更新成功');
      return true;
    } catch (error) {
      message.error('个人资料更新失败');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // 更新密码
  const handleUpdatePassword = async (values: API.ChangePasswordParams) => {
    setLoading(true);
    try {
      // 调用修改密码接口
      const response = await changePassword(values);

      if (response.success) {
        message.success('密码更新成功');
        passwordForm.resetFields();
        return true;
      } else {
        message.error(response.message || '密码更新失败');
        return false;
      }
    } catch (error: any) {
      console.error('修改密码失败:', error);
      message.error(error.message || '密码更新失败，请稍后再试');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // 头像上传配置
  const uploadProps: UploadProps = {
    name: 'avatar',
    action: '/api/upload',
    headers: {
      authorization: 'authorization-text',
    },
    showUploadList: false,
    onChange(info) {
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 上传成功`);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败`);
      }
    },
    beforeUpload(file) {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
        message.error('只能上传JPG/PNG格式的图片!');
      }
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        message.error('图片大小不能超过2MB!');
      }
      return isJpgOrPng && isLt2M;
    },
  };

  return (
    <PageContainer>
      <Card>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 24 }}>
          <Avatar
            size={64}
            src={currentUser?.avatar}
            icon={<UserOutlined />}
            style={{ marginRight: 16 }}
          />
          <div>
            <h2>{currentUser?.name}</h2>
            <p>{currentUser?.email}</p>
          </div>
        </div>

        <Tabs defaultActiveKey="basic">
          <TabPane tab="基本资料" key="basic">
            <Descriptions bordered column={2}>
              <Descriptions.Item label="姓名">{currentUser?.name}</Descriptions.Item>
              <Descriptions.Item label="邮箱">{currentUser?.email}</Descriptions.Item>
              <Descriptions.Item label="电话">{currentUser?.phone || '-'}</Descriptions.Item>
              <Descriptions.Item label="角色">{currentUser?.role === 'admin' ? '管理员' : '员工'}</Descriptions.Item>
              <Descriptions.Item label="状态">{currentUser?.isActive ? '在职' : '离职'}</Descriptions.Item>
              <Descriptions.Item label="创建时间">{currentUser?.createdAt}</Descriptions.Item>
            </Descriptions>
          </TabPane>

          <TabPane tab="修改资料" key="edit">
            <Form
              form={profileForm}
              layout="vertical"
              initialValues={{
                name: currentUser?.name,
                email: currentUser?.email,
                phone: currentUser?.phone,
              }}
              onFinish={handleUpdateProfile}
            >
              <Form.Item
                name="avatar"
                label="头像"
              >
                <Upload {...uploadProps}>
                  <Button icon={<UploadOutlined />}>上传头像</Button>
                </Upload>
              </Form.Item>
              <Form.Item
                name="name"
                label="姓名"
                rules={[{ required: true, message: '请输入姓名' }]}
              >
                <Input placeholder="请输入姓名" />
              </Form.Item>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' },
                ]}
              >
                <Input placeholder="请输入邮箱" />
              </Form.Item>
              <Form.Item
                name="phone"
                label="电话"
                rules={[
                  { pattern: /^1\d{10}$/, message: '请输入有效的手机号码' },
                ]}
              >
                <Input placeholder="请输入电话" />
              </Form.Item>
              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading}>
                  更新资料
                </Button>
              </Form.Item>
            </Form>
          </TabPane>

          <TabPane tab="修改密码" key="password">
            <Form
              form={passwordForm}
              layout="vertical"
              onFinish={handleUpdatePassword}
            >
              <Form.Item
                name="oldPassword"
                label="当前密码"
                rules={[
                  { required: true, message: '请输入当前密码' },
                ]}
              >
                <Input.Password prefix={<LockOutlined />} placeholder="请输入当前密码" />
              </Form.Item>
              <Form.Item
                name="newPassword"
                label="新密码"
                rules={[
                  { required: true, message: '请输入新密码' },
                  { min: 6, message: '密码长度不能小于6位' },
                ]}
              >
                <Input.Password prefix={<LockOutlined />} placeholder="请输入新密码" />
              </Form.Item>
              <Form.Item
                name="confirmPassword"
                label="确认新密码"
                dependencies={['newPassword']}
                rules={[
                  { required: true, message: '请确认新密码' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('newPassword') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('两次输入的密码不一致'));
                    },
                  }),
                ]}
              >
                <Input.Password prefix={<LockOutlined />} placeholder="请确认新密码" />
              </Form.Item>
              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading}>
                  更新密码
                </Button>
              </Form.Item>
            </Form>
          </TabPane>
        </Tabs>
      </Card>
    </PageContainer>
  );
};

export default Profile;
