import { Footer } from '@/components';
import { login } from '@/services/api';
import {
  AlipayCircleOutlined,
  LockOutlined,
  MobileOutlined,
  TaobaoCircleOutlined,
  UserOutlined,
  WeiboCircleOutlined,
} from '@ant-design/icons';
import {
  LoginForm,
  ProFormCaptcha,
  ProFormCheckbox,
  ProFormText,
} from '@ant-design/pro-components';
import { Helmet, history, useModel } from '@umijs/max';
import { Alert, message, Tabs } from 'antd';
import { createStyles } from 'antd-style';
import React, { useState } from 'react';
import { flushSync } from 'react-dom';
import Settings from '../../../../config/defaultSettings';

const useStyles = createStyles(({ token }) => {
  return {
    action: {
      marginLeft: '8px',
      color: 'rgba(0, 0, 0, 0.2)',
      fontSize: '24px',
      verticalAlign: 'middle',
      cursor: 'pointer',
      transition: 'color 0.3s',
      '&:hover': {
        color: token.colorPrimaryActive,
      },
    },
    lang: {
      width: 42,
      height: 42,
      lineHeight: '42px',
      position: 'fixed',
      right: 16,
      borderRadius: token.borderRadius,
      ':hover': {
        backgroundColor: token.colorBgTextHover,
      },
    },
    container: {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      backgroundImage:
        "url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')",
      backgroundSize: '100% 100%',
    },
  };
});

const ActionIcons = () => {
  const { styles } = useStyles();

  return (
    <>
      <AlipayCircleOutlined key="AlipayCircleOutlined" className={styles.action} />
      <TaobaoCircleOutlined key="TaobaoCircleOutlined" className={styles.action} />
      <WeiboCircleOutlined key="WeiboCircleOutlined" className={styles.action} />
    </>
  );
};

// 移除 Lang 组件，因为它使用了 SelectLang

const LoginMessage: React.FC<{
  content: string;
}> = ({ content }) => {
  return (
    <Alert
      style={{
        marginBottom: 24,
      }}
      message={content}
      type="error"
      showIcon
    />
  );
};

const Login: React.FC = () => {
  const [userLoginState, setUserLoginState] = useState<API.LoginResult>({});
  const [type] = useState<string>('account');
  const { initialState, setInitialState } = useModel('@@initialState');
  const { styles } = useStyles();

  const handleSubmit = async (values: API.LoginParams) => {
    try {
      // 登录
      console.log('Login values:', values);

      // 调用后端登录接口
      const response = await login(values);
      console.log('Login response:', response);

      if (response && response.token) {
        // 保存 token 到 localStorage
        localStorage.setItem('token', response.token);

        // 保存用户信息
        if (response.id) {
          localStorage.setItem('userId', response.id);
        }

        // 创建用户信息对象
        const userInfo = {
          name: response.name || '用户',
          avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
          userid: response.id,
          email: response.email || values.username,
          role: response.role || 'user',
        };

        // 缓存用户信息到 localStorage
        localStorage.setItem('userInfo', JSON.stringify(userInfo));

        message.success('登录成功！');

        // 设置初始用户信息
        await setInitialState((s) => ({
          ...s,
          currentUser: userInfo,
        }));

        // 等待一下，确保状态已经更新
        await new Promise((resolve) => setTimeout(resolve, 100));

        // 重定向到首页
        const urlParams = new URL(window.location.href).searchParams;
        const redirectPath = urlParams.get('redirect') || '/';
        console.log('重定向到:', redirectPath);
        history.push(redirectPath);
        return;
      } else {
        // 如果失败去设置用户错误信息
        setUserLoginState({ status: 'error', type: 'account' });
        message.error('用户名或密码错误');
      }
    } catch (error) {
      console.log('Login error:', error);
      message.error((error as any)?.response?.data?.message || '登录失败，请重试！');
    }
  };
  const { status, type: loginType } = userLoginState;

  return (
    <div className={styles.container}>
      <Helmet>
        <title>
          登录页
          {Settings.title && ` - ${Settings.title}`}
        </title>
      </Helmet>
      {/* 移除语言切换组件 */}
      <div
        style={{
          flex: '1',
          padding: '32px 0',
        }}
      >
        <LoginForm
          contentStyle={{
            minWidth: 280,
            maxWidth: '75vw',
          }}
          logo={<img alt="logo" src="/logo.png" />}
          title="家政后台管理系统"
          subTitle="专业的家政服务管理平台"
          initialValues={{
            autoLogin: true,
            username: '<EMAIL>',
            password: '123456',
          }}
          actions={[]}
          onFinish={async (values) => {
            await handleSubmit(values as API.LoginParams);
          }}
        >
          {/* 移除手机号登录选项卡 */}

          {status === 'error' && loginType === 'account' && (
            <LoginMessage content="账户或密码错误，请检查后重试" />
          )}
          {type === 'account' && (
            <>
              <ProFormText
                name="username"
                fieldProps={{
                  size: 'large',
                  prefix: <UserOutlined />,
                }}
                placeholder="请输入邮箱"
                rules={[
                  {
                    required: true,
                    message: '邮箱是必填项！',
                  },
                  {
                    type: 'email',
                    message: '请输入有效的邮箱地址!',
                  },
                ]}
              />
              <ProFormText.Password
                name="password"
                fieldProps={{
                  size: 'large',
                  prefix: <LockOutlined />,
                }}
                placeholder="请输入密码"
                rules={[
                  {
                    required: true,
                    message: '请输入密码！',
                  },
                ]}
              />
            </>
          )}

          {/* 移除手机号登录表单 */}
          <div
            style={{
              marginBottom: 24,
            }}
          >
            <ProFormCheckbox noStyle name="autoLogin">
              自动登录
            </ProFormCheckbox>
            <a
              style={{
                float: 'right',
              }}
            >
              忘记密码
            </a>
          </div>
        </LoginForm>
      </div>
      <Footer />
    </div>
  );
};

export default Login;
