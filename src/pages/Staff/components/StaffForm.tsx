import React from 'react';
import {
  ModalForm,
  ProFormText,
  ProFormSwitch,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import { message } from 'antd';

export type StaffFormProps = {
  title: string;
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  onFinish: (values: Record<string, any>) => Promise<boolean>;
  values?: API.UserInfo;
};

/**
 * 员工表单组件
 */
const StaffForm: React.FC<StaffFormProps> = (props) => {
  const { title, visible, onVisibleChange, onFinish, values } = props;

  return (
    <ModalForm
      title={title}
      width={640}
      open={visible}
      onOpenChange={onVisibleChange}
      onFinish={onFinish}
      initialValues={values}
    >
      <ProFormText
        name="name"
        label="姓名"
        placeholder="请输入姓名"
        rules={[
          {
            required: true,
            message: '请输入姓名',
          },
        ]}
      />
      <ProFormText
        name="email"
        label="邮箱"
        placeholder="请输入邮箱"
        rules={[
          {
            required: true,
            message: '请输入邮箱',
          },
          {
            type: 'email',
            message: '请输入有效的邮箱地址',
          },
        ]}
      />
      {!values && (
        <ProFormText.Password
          name="password"
          label="密码"
          placeholder="请输入密码"
          rules={[
            {
              required: true,
              message: '请输入密码',
            },
            {
              min: 6,
              message: '密码长度不能小于6位',
            },
          ]}
        />
      )}
      {values && (
        <ProFormText.Password
          name="password"
          label="密码"
          placeholder="如需修改密码，请输入新密码，否则留空"
        />
      )}
      <ProFormText
        name="phone"
        label="电话"
        placeholder="请输入电话"
        rules={[
          {
            pattern: /^1\d{10}$/,
            message: '请输入有效的手机号码',
          },
        ]}
      />
      <ProFormUploadButton
        name="avatar"
        label="头像"
        max={1}
        fieldProps={{
          name: 'file',
          listType: 'picture-card',
          beforeUpload: (file) => {
            const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
            if (!isJpgOrPng) {
              message.error('只能上传JPG/PNG格式的图片!');
            }
            const isLt2M = file.size / 1024 / 1024 < 2;
            if (!isLt2M) {
              message.error('图片大小不能超过2MB!');
            }
            return isJpgOrPng && isLt2M;
          },
        }}
      />
      <ProFormSwitch
        name="isActive"
        label="状态"
        checkedChildren="在职"
        unCheckedChildren="离职"
        initialValue={values?.isActive ?? true}
      />
    </ModalForm>
  );
};

export default StaffForm;
