import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  FooterToolbar,
  PageContainer,
  ProDescriptions,
  ProDescriptionsItemProps,
  ProTable,
  ProColumns,
} from '@ant-design/pro-components';
import { Button, Drawer, message, Modal, Tag, Avatar } from 'antd';
import React, { useRef, useState } from 'react';
import { getUsers, updateUser, createUser, deleteUser } from '@/services/api';
import StaffForm from './components/StaffForm';

/**
 * 员工管理页面
 */
const StaffList: React.FC = () => {
  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.UserInfo>();
  const [selectedRowsState, setSelectedRows] = useState<API.UserInfo[]>([]);

  /**
   * 添加员工
   */
  const handleAdd = async (fields: API.UserCreateParams) => {
    const hide = message.loading('正在添加');
    try {
      await createUser({ ...fields, role: 'staff' });
      hide();
      message.success('添加成功');
      return true;
    } catch (error) {
      hide();
      message.error('添加失败，请重试');
      return false;
    }
  };

  /**
   * 更新员工
   */
  const handleUpdate = async (fields: API.UserUpdateParams) => {
    const hide = message.loading('正在更新');
    try {
      if (currentRow?.id) {
        await updateUser(currentRow.id, {
          ...fields,
        });
        hide();
        message.success('更新成功');
        return true;
      }
      return false;
    } catch (error) {
      hide();
      message.error('更新失败，请重试');
      return false;
    }
  };

  /**
   * 删除员工
   */
  const handleRemove = async (selectedRows: API.UserInfo[]) => {
    const hide = message.loading('正在删除');
    if (!selectedRows) return true;
    try {
      await Promise.all(
        selectedRows.map((row) => deleteUser(row.id)),
      );
      hide();
      message.success('删除成功');
      return true;
    } catch (error) {
      hide();
      message.error('删除失败，请重试');
      return false;
    }
  };

  const columns: ProColumns<API.UserInfo>[] = [
    {
      title: '头像',
      dataIndex: 'avatar',
      valueType: 'avatar',
      hideInSearch: true,
      render: (_, record) => (
        <Avatar src={record.avatar || 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png'} />
      ),
    },
    {
      title: '姓名',
      dataIndex: 'name',
      // 添加搜索配置
      fieldProps: {
        placeholder: '请输入员工姓名',
      },
      search: true,
      render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow(entity);
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      // 添加搜索配置
      fieldProps: {
        placeholder: '请输入邮箱',
      },
      search: true,
    },
    {
      title: '电话',
      dataIndex: 'phone',
      // 添加搜索配置
      fieldProps: {
        placeholder: '请输入电话',
      },
      search: true,
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      hideInForm: false,
      valueEnum: {
        true: {
          text: '在职',
          status: 'Success',
        },
        false: {
          text: '离职',
          status: 'Error',
        },
      },
      // 添加搜索配置
      fieldProps: {
        placeholder: '请选择状态',
      },
      search: true,
      render: (_, record) => (
        <Tag color={record.isActive ? 'green' : 'red'}>
          {record.isActive ? '在职' : '离职'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateTime',
      hideInForm: true,
      hideInSearch: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        <a
          key="edit"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          编辑
        </a>,
        <a
          key="delete"
          onClick={() => {
            Modal.confirm({
              title: '确认删除',
              content: '确定要删除这个员工吗？',
              onOk: async () => {
                await handleRemove([record]);
                actionRef.current?.reloadAndRest?.();
              },
            });
          }}
        >
          删除
        </a>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.UserInfo>
        headerTitle="员工列表"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
          defaultCollapsed: false,
          optionRender: (_, formProps, dom) => [
            ...dom,
            <Button key="reset" onClick={() => {
              formProps.form?.resetFields();
              formProps.form?.submit();
            }}>
              重置
            </Button>,
          ],
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleCreateModalVisible(true);
            }}
          >
            <PlusOutlined /> 新建
          </Button>,
        ]}
        request={async (params, sort, filter) => {
          try {
            // 处理搜索参数
            const { current, pageSize, ...restParams } = params;

            // 打印完整的请求参数，便于调试
            console.log('Requesting staff with params:', {
              current,
              pageSize,
              ...restParams,
              sort,
              filter
            });

            // 发送请求到后端
            const response = await getUsers({
              // 添加分页参数
              page: current,
              limit: pageSize,
              // 添加角色参数
              role: 'staff',
              // 添加搜索参数
              ...restParams,
            });

            console.log('Staff response in component:', response);

            // 如果后端返回的是数组，则需要转换为 ProTable 需要的格式
            if (Array.isArray(response)) {
              return {
                data: response,
                success: true,
                total: response.length,
              };
            }

            // 如果后端已经返回了正确的格式
            if (response.data) {
              return {
                data: response.data,
                success: response.success,
                total: response.total,
              };
            }

            return {
              data: [],
              success: true,
              total: 0,
            };
          } catch (error) {
            console.error('获取员工列表失败:', error);
            message.error('获取员工列表失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              已选择 <a style={{ fontWeight: 600 }}>{selectedRowsState.length}</a> 项
            </div>
          }
        >
          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            批量删除
          </Button>
        </FooterToolbar>
      )}

      {/* 员工详情抽屉 */}
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.name && (
          <ProDescriptions<API.UserInfo>
            column={2}
            title={currentRow?.name}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as any}
          />
        )}
      </Drawer>

      {/* 创建员工表单 */}
      <StaffForm
        title="新建员工"
        visible={createModalVisible}
        onVisibleChange={handleCreateModalVisible}
        onFinish={async (value) => {
          const success = await handleAdd(value as API.UserCreateParams);
          if (success) {
            handleCreateModalVisible(false);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }
        }}
      />

      {/* 更新员工表单 */}
      {currentRow && (
        <StaffForm
          title="编辑员工"
          visible={updateModalVisible}
          onVisibleChange={handleUpdateModalVisible}
          onFinish={async (value) => {
            const success = await handleUpdate(value as API.UserUpdateParams);
            if (success) {
              handleUpdateModalVisible(false);
              setCurrentRow(undefined);
              if (actionRef.current) {
                actionRef.current.reload();
              }
            }
          }}
          values={currentRow}
        />
      )}
    </PageContainer>
  );
};

export default StaffList;
