import React from 'react';
import {
  ModalForm,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';

export type CustomerFormProps = {
  title: string;
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  onFinish: (values: Record<string, any>) => Promise<boolean>;
  values?: API.CustomerInfo;
};

/**
 * 客户表单组件
 */
const CustomerForm: React.FC<CustomerFormProps> = (props) => {
  const { title, visible, onVisibleChange, onFinish, values } = props;

  return (
    <ModalForm
      title={title}
      width={640}
      open={visible}
      onOpenChange={onVisibleChange}
      onFinish={onFinish}
      initialValues={values}
    >
      <ProFormText
        name="name"
        label="姓名"
        placeholder="请输入姓名"
        rules={[
          {
            required: true,
            message: '请输入姓名',
          },
        ]}
      />
      <ProFormText
        name="phone"
        label="电话"
        placeholder="请输入电话"
        rules={[
          {
            required: true,
            message: '请输入电话',
          },
          {
            pattern: /^1\d{10}$/,
            message: '请输入有效的手机号码',
          },
        ]}
      />
      <ProFormText
        name="email"
        label="邮箱"
        placeholder="请输入邮箱"
        rules={[
          {
            type: 'email',
            message: '请输入有效的邮箱地址',
          },
        ]}
      />
      <ProFormText
        name="address"
        label="地址"
        placeholder="请输入地址"
        rules={[
          {
            required: true,
            message: '请输入地址',
          },
        ]}
      />
      <ProFormTextArea
        name="notes"
        label="备注"
        placeholder="请输入备注"
      />
    </ModalForm>
  );
};

export default CustomerForm;
