import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  FooterToolbar,
  PageContainer,
  ProDescriptions,
  ProDescriptionsItemProps,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Drawer, message, Modal } from 'antd';
import React, { useRef, useState } from 'react';
import { getCustomers, updateCustomer, createCustomer, deleteCustomer } from '@/services/api';
import CustomerForm from './components/CustomerForm';

/**
 * 客户管理页面
 */
const CustomerList: React.FC = () => {
  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.CustomerInfo>();
  const [selectedRowsState, setSelectedRows] = useState<API.CustomerInfo[]>([]);

  /**
   * 添加客户
   */
  const handleAdd = async (fields: API.CustomerCreateParams) => {
    const hide = message.loading('正在添加');
    try {
      await createCustomer({ ...fields });
      hide();
      message.success('添加成功');
      return true;
    } catch (error) {
      hide();
      message.error('添加失败，请重试');
      return false;
    }
  };

  /**
   * 更新客户
   */
  const handleUpdate = async (fields: API.CustomerUpdateParams) => {
    const hide = message.loading('正在更新');
    try {
      if (currentRow?.id) {
        await updateCustomer(currentRow.id, {
          ...fields,
        });
        hide();
        message.success('更新成功');
        return true;
      }
      return false;
    } catch (error) {
      hide();
      message.error('更新失败，请重试');
      return false;
    }
  };

  /**
   * 删除客户
   */
  const handleRemove = async (selectedRows: API.CustomerInfo[]) => {
    const hide = message.loading('正在删除');
    if (!selectedRows) return true;
    try {
      await Promise.all(
        selectedRows.map((row) => deleteCustomer(row.id)),
      );
      hide();
      message.success('删除成功');
      return true;
    } catch (error) {
      hide();
      message.error('删除失败，请重试');
      return false;
    }
  };

  const columns: ProDescriptionsItemProps<API.CustomerInfo>[] = [
    {
      title: '姓名',
      dataIndex: 'name',
      render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow(entity);
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '电话',
      dataIndex: 'phone',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
    },
    {
      title: '地址',
      dataIndex: 'address',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateTime',
      hideInForm: true,
      hideInSearch: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        <a
          key="edit"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          编辑
        </a>,
        <a
          key="delete"
          onClick={() => {
            Modal.confirm({
              title: '确认删除',
              content: '确定要删除这个客户吗？',
              onOk: async () => {
                await handleRemove([record]);
                actionRef.current?.reloadAndRest?.();
              },
            });
          }}
        >
          删除
        </a>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.CustomerInfo>
        headerTitle="客户列表"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleCreateModalVisible(true);
            }}
          >
            <PlusOutlined /> 新建
          </Button>,
        ]}
        request={async (params) => {
          try {
            console.log('Requesting customers with params:', params);
            const response = await getCustomers({
              ...params,
            });
            console.log('Customers response in component:', response);

            // 如果后端返回的是数组，则需要转换为 ProTable 需要的格式
            if (Array.isArray(response)) {
              return {
                data: response,
                success: true,
                total: response.length,
              };
            }

            // 如果后端已经返回了正确的格式
            if (response.data) {
              return {
                data: response.data,
                success: response.success,
                total: response.total,
              };
            }

            return {
              data: [],
              success: true,
              total: 0,
            };
          } catch (error) {
            console.error('获取客户列表失败:', error);
            message.error('获取客户列表失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              已选择 <a style={{ fontWeight: 600 }}>{selectedRowsState.length}</a> 项
            </div>
          }
        >
          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            批量删除
          </Button>
        </FooterToolbar>
      )}

      {/* 客户详情抽屉 */}
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.name && (
          <ProDescriptions<API.CustomerInfo>
            column={2}
            title={currentRow?.name}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<API.CustomerInfo>[]}
          />
        )}
      </Drawer>

      {/* 创建客户表单 */}
      <CustomerForm
        title="新建客户"
        visible={createModalVisible}
        onVisibleChange={handleCreateModalVisible}
        onFinish={async (value) => {
          const success = await handleAdd(value as API.CustomerCreateParams);
          if (success) {
            handleCreateModalVisible(false);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }
        }}
      />

      {/* 更新客户表单 */}
      {currentRow && (
        <CustomerForm
          title="编辑客户"
          visible={updateModalVisible}
          onVisibleChange={handleUpdateModalVisible}
          onFinish={async (value) => {
            const success = await handleUpdate(value as API.CustomerUpdateParams);
            if (success) {
              handleUpdateModalVisible(false);
              setCurrentRow(undefined);
              if (actionRef.current) {
                actionRef.current.reload();
              }
            }
          }}
          values={currentRow}
        />
      )}
    </PageContainer>
  );
};

export default CustomerList;
