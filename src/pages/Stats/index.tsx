import React, { useEffect, useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { Card, Row, Col, Statistic, Spin, Tabs, message } from 'antd';
import { getStats } from '@/services/api';
import OrderStats from './components/OrderStats';
import RevenueStats from './components/RevenueStats';
import ServiceStats from './components/ServiceStats';
import StaffStats from './components/StaffStats';

const { TabPane } = Tabs;

/**
 * 统计分析页面
 */
const Stats: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [stats, setStats] = useState<API.StatsData | null>(null);

  // 获取统计数据
  useEffect(() => {
    const fetchStats = async () => {
      setLoading(true);
      try {
        const response = await getStats();
        setStats(response.data);
      } catch (error) {
        message.error('获取统计数据失败');
        // 使用模拟数据
        setStats({
          orderStats: {
            total: 120,
            completed: 85,
            pending: 30,
            cancelled: 5,
          },
          revenueStats: {
            total: 25000,
            monthly: 8500,
            weekly: 2100,
            daily: 300,
          },
          serviceStats: {
            mostPopular: [
              {
                id: '1',
                name: '日常家居保洁',
                count: 45,
              },
              {
                id: '2',
                name: '深度清洁',
                count: 30,
              },
            ],
            categories: [
              {
                name: '日常保洁',
                count: 50,
              },
              {
                name: '深度清洁',
                count: 30,
              },
            ],
          },
          customerStats: {
            total: 80,
            new: 15,
            returning: 65,
          },
          staffStats: {
            total: 12,
            active: 10,
            topPerformers: [
              {
                id: '1',
                name: '张三',
                ordersCompleted: 25,
              },
              {
                id: '2',
                name: '李四',
                ordersCompleted: 20,
              },
            ],
          },
        });
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  return (
    <PageContainer>
      <Spin spinning={loading}>
        {stats && (
          <>
            {/* 概览统计卡片 */}
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="总订单数"
                    value={stats.orderStats.total}
                    suffix="单"
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="总收入"
                    value={stats.revenueStats.total}
                    precision={2}
                    prefix="¥"
                    suffix="元"
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="客户总数"
                    value={stats.customerStats.total}
                    suffix="人"
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="员工总数"
                    value={stats.staffStats.total}
                    suffix="人"
                  />
                </Card>
              </Col>
            </Row>

            {/* 详细统计数据 */}
            <Card>
              <Tabs defaultActiveKey="order">
                <TabPane tab="订单统计" key="order">
                  <OrderStats stats={stats.orderStats} />
                </TabPane>
                <TabPane tab="收入统计" key="revenue">
                  <RevenueStats stats={stats.revenueStats} />
                </TabPane>
                <TabPane tab="服务统计" key="service">
                  <ServiceStats stats={stats.serviceStats} />
                </TabPane>
                <TabPane tab="员工统计" key="staff">
                  <StaffStats stats={stats.staffStats} />
                </TabPane>
              </Tabs>
            </Card>
          </>
        )}
      </Spin>
    </PageContainer>
  );
};

export default Stats;
