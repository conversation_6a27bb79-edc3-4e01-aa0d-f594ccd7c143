import React from 'react';
import { Card, Row, Col, Statistic, List, Avatar, Progress } from 'antd';
import { UserOutlined } from '@ant-design/icons';

interface StaffStatsProps {
  stats: {
    total: number;
    active: number;
    topPerformers: {
      id: string;
      name: string;
      ordersCompleted: number;
    }[];
  };
}

/**
 * 员工统计组件
 */
const StaffStats: React.FC<StaffStatsProps> = ({ stats }) => {
  // 计算最高订单数
  const maxOrders = Math.max(...stats.topPerformers.map((staff) => staff.ordersCompleted));

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={12}>
          <Card>
            <Statistic
              title="员工总数"
              value={stats.total}
              suffix="人"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card>
            <Statistic
              title="活跃员工"
              value={stats.active}
              suffix="人"
              valueStyle={{ color: '#3f8600' }}
            />
            <div style={{ marginTop: 8 }}>
              <Progress
                percent={Math.round((stats.active / stats.total) * 100)}
                status="active"
                strokeColor="#3f8600"
              />
            </div>
          </Card>
        </Col>
      </Row>

      <Card title="绩效最佳员工">
        <List
          itemLayout="horizontal"
          dataSource={stats.topPerformers}
          renderItem={(item, index) => (
            <List.Item>
              <List.Item.Meta
                avatar={
                  <Avatar icon={<UserOutlined />} style={{ backgroundColor: index === 0 ? '#f56a00' : '#1890ff' }} />
                }
                title={`${index + 1}. ${item.name}`}
                description={
                  <div>
                    <div>完成订单数: {item.ordersCompleted}</div>
                    <Progress
                      percent={Math.round((item.ordersCompleted / maxOrders) * 100)}
                      status="active"
                      strokeColor={index === 0 ? '#f56a00' : '#1890ff'}
                    />
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </Card>
    </div>
  );
};

export default StaffStats;
