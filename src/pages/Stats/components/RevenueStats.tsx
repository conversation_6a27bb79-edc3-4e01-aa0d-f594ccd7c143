import React from 'react';
import { Card, Row, Col, Statistic } from 'antd';
import { Column } from '@ant-design/plots';

interface RevenueStatsProps {
  stats: {
    total: number;
    monthly: number;
    weekly: number;
    daily: number;
  };
}

/**
 * 收入统计组件
 */
const RevenueStats: React.FC<RevenueStatsProps> = ({ stats }) => {
  // 柱状图数据
  const columnData = [
    {
      type: '今日',
      sales: stats.daily,
    },
    {
      type: '本周',
      sales: stats.weekly,
    },
    {
      type: '本月',
      sales: stats.monthly,
    },
    {
      type: '总计',
      sales: stats.total,
    },
  ];

  // 柱状图配置
  const columnConfig = {
    data: columnData,
    xField: 'type',
    yField: 'sales',
    label: {
      position: 'middle',
      style: {
        fill: '#FFFFFF',
        opacity: 0.6,
      },
    },
    meta: {
      type: { alias: '时间' },
      sales: { alias: '收入' },
    },
  };

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总收入"
              value={stats.total}
              precision={2}
              prefix="¥"
              suffix="元"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="本月收入"
              value={stats.monthly}
              precision={2}
              prefix="¥"
              suffix="元"
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="本周收入"
              value={stats.weekly}
              precision={2}
              prefix="¥"
              suffix="元"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日收入"
              value={stats.daily}
              precision={2}
              prefix="¥"
              suffix="元"
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      <Card title="收入统计">
        <div style={{ height: 400 }}>
          <Column {...columnConfig} />
        </div>
      </Card>
    </div>
  );
};

export default RevenueStats;
