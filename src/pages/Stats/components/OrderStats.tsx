import React from 'react';
import { Card, Row, Col, Statistic } from 'antd';
import { Pie } from '@ant-design/plots';

interface OrderStatsProps {
  stats: {
    total: number;
    completed: number;
    pending: number;
    cancelled: number;
  };
}

/**
 * 订单统计组件
 */
const OrderStats: React.FC<OrderStatsProps> = ({ stats }) => {
  // 饼图数据
  const pieData = [
    {
      type: '已完成',
      value: stats.completed,
    },
    {
      type: '待处理',
      value: stats.pending,
    },
    {
      type: '已取消',
      value: stats.cancelled,
    },
  ];

  // 饼图配置
  const pieConfig = {
    appendPadding: 10,
    data: pieData,
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name} {percentage}',
    },
    interactions: [
      {
        type: 'pie-legend-active',
      },
      {
        type: 'element-active',
      },
    ],
  };

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总订单数"
              value={stats.total}
              suffix="单"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已完成订单"
              value={stats.completed}
              suffix="单"
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待处理订单"
              value={stats.pending}
              suffix="单"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已取消订单"
              value={stats.cancelled}
              suffix="单"
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      <Card title="订单状态分布">
        <div style={{ height: 400 }}>
          <Pie {...pieConfig} />
        </div>
      </Card>
    </div>
  );
};

export default OrderStats;
