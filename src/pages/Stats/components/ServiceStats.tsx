import React from 'react';
import { Card, List, Typography } from 'antd';
import { Pie } from '@ant-design/plots';

const { Title } = Typography;

interface ServiceStatsProps {
  stats: {
    mostPopular: {
      id: string;
      name: string;
      count: number;
    }[];
    categories: {
      name: string;
      count: number;
    }[];
  };
}

/**
 * 服务统计组件
 */
const ServiceStats: React.FC<ServiceStatsProps> = ({ stats }) => {
  // 饼图数据
  const pieData = stats.categories.map((category) => ({
    type: category.name,
    value: category.count,
  }));

  // 饼图配置
  const pieConfig = {
    appendPadding: 10,
    data: pieData,
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name} {percentage}',
    },
    interactions: [
      {
        type: 'pie-legend-active',
      },
      {
        type: 'element-active',
      },
    ],
  };

  return (
    <div>
      <Card title="服务类别分布" style={{ marginBottom: 24 }}>
        <div style={{ height: 400 }}>
          <Pie {...pieConfig} />
        </div>
      </Card>

      <Card title="最受欢迎的服务">
        <List
          itemLayout="horizontal"
          dataSource={stats.mostPopular}
          renderItem={(item, index) => (
            <List.Item>
              <List.Item.Meta
                avatar={<div style={{ width: 24, textAlign: 'center' }}>{index + 1}</div>}
                title={item.name}
                description={`订单数量: ${item.count}`}
              />
            </List.Item>
          )}
        />
      </Card>
    </div>
  );
};

export default ServiceStats;
