import React, { useEffect, useState } from 'react';
import {
  ModalForm,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormDateTimePicker,
  ProFormDigit,
} from '@ant-design/pro-components';
import { message } from 'antd';
import { getCustomers, getServices, getUsers } from '@/services/api';

export type OrderFormProps = {
  title: string;
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  onFinish: (values: Record<string, any>) => Promise<void>;
  values?: API.OrderInfo;
};

/**
 * 订单表单组件
 */
const OrderForm: React.FC<OrderFormProps> = (props) => {
  const { title, visible, onVisibleChange, onFinish, values } = props;
  const [customers, setCustomers] = useState<{ label: string; value: string }[]>([]);
  const [services, setServices] = useState<{ label: string; value: string }[]>([]);
  const [staff, setStaff] = useState<{ label: string; value: string }[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // 获取客户、服务和员工数据
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // 获取客户数据
        const customersResponse = await getCustomers();
        if (customersResponse.data) {
          setCustomers(
            customersResponse.data.map((customer) => ({
              label: customer.name,
              value: customer.id,
            })),
          );
        }

        // 获取服务数据
        const servicesResponse = await getServices();
        if (servicesResponse.data) {
          setServices(
            servicesResponse.data.map((service) => ({
              label: service.name,
              value: service.id,
            })),
          );
        }

        // 获取员工数据
        const staffResponse = await getUsers();
        if (staffResponse.data) {
          setStaff(
            staffResponse.data
              .filter((user) => user.role === 'staff')
              .map((user) => ({
                label: user.name,
                value: user.id,
              })),
          );
        }
      } catch (error) {
        message.error('获取数据失败');
      } finally {
        setLoading(false);
      }
    };

    if (visible) {
      fetchData();
    }
  }, [visible]);

  return (
    <ModalForm
      title={title}
      width={640}
      open={visible}
      onOpenChange={onVisibleChange}
      onFinish={onFinish}
      initialValues={values}
      submitter={{
        searchConfig: {
          submitText: values ? '更新' : '创建',
        },
        resetButtonProps: {
          disabled: loading,
        },
        submitButtonProps: {
          disabled: loading,
        },
      }}
    >
      <ProFormSelect
        name="customerId"
        label="客户"
        placeholder="请选择客户"
        options={customers}
        rules={[
          {
            required: true,
            message: '请选择客户',
          },
        ]}
        disabled={loading}
      />
      <ProFormSelect
        name="serviceId"
        label="服务"
        placeholder="请选择服务"
        options={services}
        rules={[
          {
            required: true,
            message: '请选择服务',
          },
        ]}
        disabled={loading}
      />
      <ProFormSelect
        name="staffId"
        label="员工"
        placeholder="请选择员工"
        options={staff}
        disabled={loading}
      />
      <ProFormDateTimePicker
        name="scheduledAt"
        label="预约时间"
        placeholder="请选择预约时间"
        rules={[
          {
            required: true,
            message: '请选择预约时间',
          },
        ]}
      />
      <ProFormText
        name="address"
        label="服务地址"
        placeholder="请输入服务地址"
        rules={[
          {
            required: true,
            message: '请输入服务地址',
          },
        ]}
      />
      <ProFormDigit
        name="price"
        label="价格"
        placeholder="请输入价格"
        min={0}
        fieldProps={{
          precision: 2,
          formatter: (value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
          parser: (value) => value!.replace(/\¥\s?|(,*)/g, ''),
        }}
        rules={[
          {
            required: true,
            message: '请输入价格',
          },
        ]}
      />
      <ProFormSelect
        name="status"
        label="订单状态"
        placeholder="请选择订单状态"
        options={[
          {
            value: 'pending',
            label: '待处理',
          },
          {
            value: 'confirmed',
            label: '已确认',
          },
          {
            value: 'in_progress',
            label: '进行中',
          },
          {
            value: 'completed',
            label: '已完成',
          },
          {
            value: 'cancelled',
            label: '已取消',
          },
        ]}
        initialValue={values?.status || 'pending'}
      />
      <ProFormSelect
        name="paymentStatus"
        label="支付状态"
        placeholder="请选择支付状态"
        options={[
          {
            value: 'unpaid',
            label: '未支付',
          },
          {
            value: 'paid',
            label: '已支付',
          },
          {
            value: 'refunded',
            label: '已退款',
          },
        ]}
        initialValue={values?.paymentStatus || 'unpaid'}
      />
      <ProFormSelect
        name="paymentMethod"
        label="支付方式"
        placeholder="请选择支付方式"
        options={[
          {
            value: 'cash',
            label: '现金',
          },
          {
            value: 'wechat',
            label: '微信支付',
          },
          {
            value: 'alipay',
            label: '支付宝',
          },
          {
            value: 'card',
            label: '银行卡',
          },
        ]}
      />
      <ProFormTextArea
        name="notes"
        label="备注"
        placeholder="请输入备注"
      />
    </ModalForm>
  );
};

export default OrderForm;
