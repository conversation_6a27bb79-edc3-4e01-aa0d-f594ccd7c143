import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  FooterToolbar,
  PageContainer,
  ProDescriptions,
  ProTable,
  ProColumns,
} from '@ant-design/pro-components';
import { Button, Drawer, message, Modal, Tag } from 'antd';
import React, { useRef, useState } from 'react';
import { getOrders, updateOrder, createOrder, deleteOrder } from '@/services/api';
import OrderForm from './components/OrderForm';

/**
 * 订单管理页面
 */
const OrderList: React.FC = () => {
  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.OrderInfo>();
  const [selectedRowsState, setSelectedRows] = useState<API.OrderInfo[]>([]);

  /**
   * 添加订单
   */
  const handleAdd = async (fields: API.OrderCreateParams) => {
    const hide = message.loading('正在添加');
    try {
      await createOrder({ ...fields });
      hide();
      message.success('添加成功');
      return true;
    } catch (error) {
      hide();
      message.error('添加失败，请重试');
      return false;
    }
  };

  /**
   * 更新订单
   */
  const handleUpdate = async (fields: API.OrderUpdateParams) => {
    const hide = message.loading('正在更新');
    try {
      if (currentRow?.id) {
        await updateOrder(currentRow.id, {
          ...fields,
        });
        hide();
        message.success('更新成功');
        return true;
      }
      return false;
    } catch (error) {
      hide();
      message.error('更新失败，请重试');
      return false;
    }
  };

  /**
   * 删除订单
   */
  const handleRemove = async (selectedRows: API.OrderInfo[]) => {
    const hide = message.loading('正在删除');
    if (!selectedRows) return true;
    try {
      await Promise.all(
        selectedRows.map((row) => deleteOrder(row.id)),
      );
      hide();
      message.success('删除成功');
      return true;
    } catch (error) {
      hide();
      message.error('删除失败，请重试');
      return false;
    }
  };

  const columns: ProColumns<API.OrderInfo>[] = [
    {
      title: '订单号',
      dataIndex: 'orderNumber',
      hideInForm: true,
      render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow(entity);
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '客户',
      dataIndex: 'customerName',
      hideInForm: true,
      // 添加搜索配置
      fieldProps: {
        placeholder: '请输入客户姓名',
      },
      search: true,
    },
    {
      title: '服务',
      dataIndex: 'serviceName',
      hideInForm: true,
    },
    {
      title: '员工',
      dataIndex: 'staffName',
      hideInForm: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: {
        'pending': { text: '待处理', status: 'warning' },
        'confirmed': { text: '已确认', status: 'processing' },
        'in_progress': { text: '进行中', status: 'processing' },
        'completed': { text: '已完成', status: 'success' },
        'cancelled': { text: '已取消', status: 'error' },
      },
      // 添加搜索配置
      fieldProps: {
        placeholder: '请选择订单状态',
      },
      search: true,
      render: (_, record) => {
        let color = 'default';
        let text = '未知状态';

        switch (record.status) {
          case 'pending':
            color = 'orange';
            text = '待处理';
            break;
          case 'confirmed':
            color = 'blue';
            text = '已确认';
            break;
          case 'in_progress':
            color = 'geekblue';
            text = '进行中';
            break;
          case 'completed':
            color = 'green';
            text = '已完成';
            break;
          case 'cancelled':
            color = 'red';
            text = '已取消';
            break;
        }

        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '预约时间',
      dataIndex: 'scheduledAt',
      valueType: 'dateTime',
    },
    {
      title: '价格',
      dataIndex: 'price',
      valueType: 'money',
    },
    {
      title: '支付状态',
      dataIndex: 'paymentStatus',
      valueEnum: {
        'unpaid': { text: '未支付', status: 'warning' },
        'paid': { text: '已支付', status: 'success' },
        'refunded': { text: '已退款', status: 'error' },
      },
      // 添加搜索配置
      fieldProps: {
        placeholder: '请选择支付状态',
      },
      search: true,
      render: (_, record) => {
        let color = 'default';
        let text = '未知状态';

        switch (record.paymentStatus) {
          case 'unpaid':
            color = 'orange';
            text = '未支付';
            break;
          case 'paid':
            color = 'green';
            text = '已支付';
            break;
          case 'refunded':
            color = 'red';
            text = '已退款';
            break;
        }

        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateTime',
      hideInForm: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        <a
          key="edit"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          编辑
        </a>,
        <a
          key="delete"
          onClick={() => {
            Modal.confirm({
              title: '确认删除',
              content: '确定要删除这个订单吗？',
              onOk: async () => {
                await handleRemove([record]);
                actionRef.current?.reloadAndRest?.();
              },
            });
          }}
        >
          删除
        </a>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.OrderInfo>
        headerTitle="订单列表"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
          defaultCollapsed: false,
          optionRender: (_, formProps, dom) => [
            ...dom,
            <Button key="reset" onClick={() => {
              formProps.form?.resetFields();
              formProps.form?.submit();
            }}>
              重置
            </Button>,
          ],
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleCreateModalVisible(true);
            }}
          >
            <PlusOutlined /> 新建
          </Button>,
        ]}
        request={async (params, sort, filter) => {
          try {
            // 处理搜索参数
            const { current, pageSize, ...restParams } = params;

            // 打印完整的请求参数，便于调试
            console.log('Requesting orders with params:', {
              current,
              pageSize,
              ...restParams,
              sort,
              filter
            });

            // 发送请求到后端
            const response = await getOrders({
              // 添加分页参数
              page: current,
              limit: pageSize,
              // 添加搜索参数
              ...restParams,
            });

            console.log('Orders response in component:', response);

            // 如果后端返回的是数组，则需要转换为 ProTable 需要的格式
            if (Array.isArray(response)) {
              return {
                data: response,
                success: true,
                total: response.length,
              };
            }

            // 如果后端已经返回了正确的格式
            if (response.data) {
              return {
                data: response.data,
                success: response.success,
                total: response.total,
              };
            }

            return {
              data: [],
              success: true,
              total: 0,
            };
          } catch (error) {
            console.error('获取订单列表失败:', error);
            message.error('获取订单列表失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              已选择 <a style={{ fontWeight: 600 }}>{selectedRowsState.length}</a> 项
            </div>
          }
        >
          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            批量删除
          </Button>
        </FooterToolbar>
      )}

      {/* 订单详情抽屉 */}
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.orderNumber && (
          <ProDescriptions<API.OrderInfo>
            column={2}
            title={`订单号: ${currentRow?.orderNumber}`}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as any}
          />
        )}
      </Drawer>

      {/* 创建订单表单 */}
      <OrderForm
        title="新建订单"
        visible={createModalVisible}
        onVisibleChange={handleCreateModalVisible}
        onFinish={async (value) => {
          await handleAdd(value as API.OrderCreateParams);
          handleCreateModalVisible(false);
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      />

      {/* 更新订单表单 */}
      {currentRow && (
        <OrderForm
          title="编辑订单"
          visible={updateModalVisible}
          onVisibleChange={handleUpdateModalVisible}
          onFinish={async (value) => {
            await handleUpdate(value as API.OrderUpdateParams);
            handleUpdateModalVisible(false);
            setCurrentRow(undefined);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }}
          values={currentRow}
        />
      )}
    </PageContainer>
  );
};

export default OrderList;
