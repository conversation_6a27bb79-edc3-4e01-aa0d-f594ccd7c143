import React, { useEffect, useState } from 'react';
import {
  ModalForm,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormDatePicker,
  ProFormDigit,
} from '@ant-design/pro-components';
import { message } from 'antd';
import { getOrders } from '@/services/api';

export type FinanceFormProps = {
  title: string;
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  onFinish: (values: Record<string, any>) => Promise<boolean>;
  values?: API.FinanceInfo;
};

/**
 * 财务表单组件
 */
const FinanceForm: React.FC<FinanceFormProps> = (props) => {
  const { title, visible, onVisibleChange, onFinish, values } = props;
  const [orders, setOrders] = useState<{ label: string; value: string }[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [financeType, setFinanceType] = useState<string>(values?.type || 'income');

  // 获取订单数据
  useEffect(() => {
    const fetchOrders = async () => {
      setLoading(true);
      try {
        const response = await getOrders();
        if (response.data) {
          setOrders(
            response.data.map((order) => ({
              label: `${order.orderNumber} - ${order.customerName} - ${order.serviceName}`,
              value: order.id,
            })),
          );
        }
      } catch (error) {
        message.error('获取订单数据失败');
      } finally {
        setLoading(false);
      }
    };

    if (visible) {
      fetchOrders();
    }
  }, [visible]);

  return (
    <ModalForm
      title={title}
      width={640}
      open={visible}
      onOpenChange={onVisibleChange}
      onFinish={onFinish}
      initialValues={values}
    >
      <ProFormSelect
        name="type"
        label="类型"
        options={[
          {
            value: 'income',
            label: '收入',
          },
          {
            value: 'expense',
            label: '支出',
          },
        ]}
        rules={[
          {
            required: true,
            message: '请选择类型',
          },
        ]}
        fieldProps={{
          onChange: (value) => setFinanceType(value),
        }}
      />
      {financeType === 'income' && (
        <ProFormSelect
          name="orderId"
          label="关联订单"
          placeholder="请选择关联订单"
          options={orders}
          disabled={loading}
        />
      )}
      <ProFormDigit
        name="amount"
        label="金额"
        placeholder="请输入金额"
        min={0}
        fieldProps={{
          precision: 2,
          formatter: (value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
          parser: (value) => value!.replace(/\¥\s?|(,*)/g, ''),
        }}
        rules={[
          {
            required: true,
            message: '请输入金额',
          },
        ]}
      />
      <ProFormText
        name="description"
        label="描述"
        placeholder="请输入描述"
        rules={[
          {
            required: true,
            message: '请输入描述',
          },
        ]}
      />
      <ProFormDatePicker
        name="date"
        label="日期"
        placeholder="请选择日期"
        rules={[
          {
            required: true,
            message: '请选择日期',
          },
        ]}
      />
      <ProFormSelect
        name="status"
        label="状态"
        placeholder="请选择状态"
        options={[
          {
            value: 'pending',
            label: '待处理',
          },
          {
            value: 'completed',
            label: '已完成',
          },
          {
            value: 'cancelled',
            label: '已取消',
          },
        ]}
        initialValue={values?.status || 'pending'}
      />
      <ProFormTextArea
        name="notes"
        label="备注"
        placeholder="请输入备注"
      />
    </ModalForm>
  );
};

export default FinanceForm;
