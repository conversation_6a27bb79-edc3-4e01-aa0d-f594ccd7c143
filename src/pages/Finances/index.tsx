import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  FooterToolbar,
  PageContainer,
  ProDescriptions,
  ProDescriptionsItemProps,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Drawer, message, Modal, Tag, Statistic, Card, Row, Col } from 'antd';
import React, { useRef, useState, useEffect } from 'react';
import { getFinances } from '@/services/api';
import FinanceForm from './components/FinanceForm';

/**
 * 财务管理页面
 */
const FinanceList: React.FC = () => {
  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.FinanceInfo>();
  const [selectedRowsState, setSelectedRows] = useState<API.FinanceInfo[]>([]);
  const [stats, setStats] = useState<{
    totalIncome: number;
    totalExpense: number;
    balance: number;
  }>({
    totalIncome: 0,
    totalExpense: 0,
    balance: 0,
  });

  // 获取财务统计数据
  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await getFinances();
        if (response.data) {
          const income = response.data
            .filter((item) => item.type === 'income')
            .reduce((sum, item) => sum + item.amount, 0);
          
          const expense = response.data
            .filter((item) => item.type === 'expense')
            .reduce((sum, item) => sum + item.amount, 0);
          
          setStats({
            totalIncome: income,
            totalExpense: expense,
            balance: income - expense,
          });
        }
      } catch (error) {
        message.error('获取财务统计数据失败');
      }
    };

    fetchStats();
  }, []);

  /**
   * 添加财务记录
   */
  const handleAdd = async (fields: any) => {
    const hide = message.loading('正在添加');
    try {
      // 模拟添加财务记录
      hide();
      message.success('添加成功');
      return true;
    } catch (error) {
      hide();
      message.error('添加失败，请重试');
      return false;
    }
  };

  const columns: ProDescriptionsItemProps<API.FinanceInfo>[] = [
    {
      title: '订单号',
      dataIndex: 'orderNumber',
      render: (dom, entity) => {
        return entity.orderNumber ? (
          <a
            onClick={() => {
              setCurrentRow(entity);
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        ) : (
          '-'
        );
      },
    },
    {
      title: '类型',
      dataIndex: 'type',
      valueEnum: {
        'income': { text: '收入', status: 'success' },
        'expense': { text: '支出', status: 'error' },
      },
      render: (_, record) => (
        <Tag color={record.type === 'income' ? 'green' : 'red'}>
          {record.type === 'income' ? '收入' : '支出'}
        </Tag>
      ),
    },
    {
      title: '金额',
      dataIndex: 'amount',
      valueType: 'money',
      render: (_, record) => (
        <span style={{ color: record.type === 'income' ? 'green' : 'red' }}>
          {record.type === 'income' ? '+' : '-'} ¥{record.amount.toFixed(2)}
        </span>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      ellipsis: true,
    },
    {
      title: '日期',
      dataIndex: 'date',
      valueType: 'date',
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: {
        'pending': { text: '待处理', status: 'warning' },
        'completed': { text: '已完成', status: 'success' },
        'cancelled': { text: '已取消', status: 'error' },
      },
      render: (_, record) => {
        let color = 'default';
        let text = '未知状态';
        
        switch (record.status) {
          case 'pending':
            color = 'orange';
            text = '待处理';
            break;
          case 'completed':
            color = 'green';
            text = '已完成';
            break;
          case 'cancelled':
            color = 'red';
            text = '已取消';
            break;
        }
        
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateTime',
      hideInForm: true,
      hideInSearch: true,
    },
  ];

  return (
    <PageContainer>
      {/* 财务统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="总收入"
              value={stats.totalIncome}
              precision={2}
              valueStyle={{ color: '#3f8600' }}
              prefix="¥"
              suffix="元"
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="总支出"
              value={stats.totalExpense}
              precision={2}
              valueStyle={{ color: '#cf1322' }}
              prefix="¥"
              suffix="元"
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="结余"
              value={stats.balance}
              precision={2}
              valueStyle={{ color: stats.balance >= 0 ? '#3f8600' : '#cf1322' }}
              prefix="¥"
              suffix="元"
            />
          </Card>
        </Col>
      </Row>

      <ProTable<API.FinanceInfo>
        headerTitle="财务记录"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleCreateModalVisible(true);
            }}
          >
            <PlusOutlined /> 新建
          </Button>,
        ]}
        request={async (params) => {
          try {
            const response = await getFinances({
              ...params,
            });
            return {
              data: response.data || [],
              success: true,
              total: response.total || 0,
            };
          } catch (error) {
            message.error('获取财务记录失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />

      {/* 财务记录详情抽屉 */}
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions<API.FinanceInfo>
            column={2}
            title="财务记录详情"
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<API.FinanceInfo>[]}
          />
        )}
      </Drawer>

      {/* 创建财务记录表单 */}
      <FinanceForm
        title="新建财务记录"
        visible={createModalVisible}
        onVisibleChange={handleCreateModalVisible}
        onFinish={async (value) => {
          const success = await handleAdd(value);
          if (success) {
            handleCreateModalVisible(false);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }
        }}
      />
    </PageContainer>
  );
};

export default FinanceList;
