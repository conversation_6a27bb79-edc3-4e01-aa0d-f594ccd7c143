import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  FooterToolbar,
  PageContainer,
  ProDescriptions,
  ProDescriptionsItemProps,
  ProTable,
  ProColumns,
} from '@ant-design/pro-components';
import { Button, Drawer, message, Modal, Input } from 'antd';
import React, { useRef, useState } from 'react';
import { getServices, updateService, createService, deleteService } from '@/services/api';
import ServiceForm from './components/ServiceForm';

/**
 * 服务管理页面
 */
const ServiceList: React.FC = () => {
  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.ServiceInfo>();
  const [selectedRowsState, setSelectedRows] = useState<API.ServiceInfo[]>([]);

  /**
   * 添加服务
   */
  const handleAdd = async (fields: API.ServiceCreateParams) => {
    const hide = message.loading('正在添加');
    try {
      await createService({ ...fields });
      hide();
      message.success('添加成功');
      return true;
    } catch (error) {
      hide();
      message.error('添加失败，请重试');
      return false;
    }
  };

  /**
   * 更新服务
   */
  const handleUpdate = async (fields: API.ServiceUpdateParams) => {
    const hide = message.loading('正在更新');
    try {
      if (currentRow?.id) {
        await updateService(currentRow.id, {
          ...fields,
        });
        hide();
        message.success('更新成功');
        return true;
      }
      return false;
    } catch (error) {
      hide();
      message.error('更新失败，请重试');
      return false;
    }
  };

  /**
   * 删除服务
   */
  const handleRemove = async (selectedRows: API.ServiceInfo[]) => {
    const hide = message.loading('正在删除');
    if (!selectedRows) return true;
    try {
      await Promise.all(
        selectedRows.map((row) => deleteService(row.id)),
      );
      hide();
      message.success('删除成功');
      return true;
    } catch (error) {
      hide();
      message.error('删除失败，请重试');
      return false;
    }
  };

  const columns: ProColumns<API.ServiceInfo>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      hideInForm: true,
      render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow(entity);
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '服务名称',
      dataIndex: 'name',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '服务名称为必填项',
          },
        ],
      },
      // 添加搜索配置
      fieldProps: {
        placeholder: '请输入服务名称',
      },
      search: true,
    },
    {
      title: '服务描述',
      dataIndex: 'description',
      valueType: 'textarea',
    },
    {
      title: '价格',
      dataIndex: 'price',
      valueType: 'money',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '价格为必填项',
          },
        ],
      },
      // 添加搜索配置
      fieldProps: {
        placeholder: '请输入价格',
      },
      search: true,
    },
    {
      title: '时长（小时）',
      dataIndex: 'duration',
      valueType: 'digit',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '时长为必填项',
          },
        ],
      },
    },
    {
      title: '类别',
      dataIndex: 'category',
      valueEnum: {
        '日常保洁': { text: '日常保洁' },
        '深度清洁': { text: '深度清洁' },
        '专项服务': { text: '专项服务' },
        '家电清洗': { text: '家电清洗' },
        '其他': { text: '其他' },
      },
      // 添加搜索配置
      fieldProps: {
        placeholder: '请选择服务类别',
      },
      search: true,
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      hideInForm: false,
      valueEnum: {
        true: {
          text: '启用',
          status: 'Success',
        },
        false: {
          text: '禁用',
          status: 'Error',
        },
      },
      // 添加搜索配置
      fieldProps: {
        placeholder: '请选择状态',
      },
      search: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateTime',
      hideInForm: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      valueType: 'dateTime',
      hideInForm: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        <a
          key="edit"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          编辑
        </a>,
        <a
          key="delete"
          onClick={() => {
            Modal.confirm({
              title: '确认删除',
              content: '确定要删除这个服务吗？',
              onOk: async () => {
                await handleRemove([record]);
                actionRef.current?.reloadAndRest?.();
              },
            });
          }}
        >
          删除
        </a>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.ServiceInfo>
        headerTitle="服务列表"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
          defaultCollapsed: false,
          optionRender: (searchConfig, formProps, dom) => [
            ...dom,
            <Button key="reset" onClick={() => {
              formProps.form?.resetFields();
              formProps.form?.submit();
            }}>
              重置
            </Button>,
          ],
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleCreateModalVisible(true);
            }}
          >
            <PlusOutlined /> 新建
          </Button>,
        ]}
        request={async (params, sort, filter) => {
          try {
            // 处理搜索参数
            const { current, pageSize, ...restParams } = params;

            // 打印完整的请求参数，便于调试
            console.log('Requesting services with params:', {
              current,
              pageSize,
              ...restParams,
              sort,
              filter
            });

            // 发送请求到后端
            const response = await getServices({
              // 添加分页参数
              page: current,
              limit: pageSize,
              // 添加搜索参数
              ...restParams,
            });

            console.log('Services response in component:', response);

            // 如果后端返回的是数组，则需要转换为 ProTable 需要的格式
            if (Array.isArray(response)) {
              return {
                data: response,
                success: true,
                total: response.length,
              };
            }

            // 如果后端已经返回了正确的格式
            if (response.data) {
              return {
                data: response.data,
                success: response.success,
                total: response.total,
              };
            }

            return {
              data: [],
              success: true,
              total: 0,
            };
          } catch (error) {
            console.error('获取服务列表失败:', error);
            message.error('获取服务列表失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              已选择 <a style={{ fontWeight: 600 }}>{selectedRowsState.length}</a> 项
            </div>
          }
        >
          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            批量删除
          </Button>
        </FooterToolbar>
      )}

      {/* 创建服务表单 */}
      <ServiceForm
        title="新建服务"
        visible={createModalVisible}
        onVisibleChange={handleCreateModalVisible}
        onFinish={async (value) => {
          const success = await handleAdd(value as API.ServiceCreateParams);
          if (success) {
            handleCreateModalVisible(false);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }
        }}
      />

      {/* 更新服务表单 */}
      {currentRow && (
        <ServiceForm
          title="编辑服务"
          visible={updateModalVisible}
          onVisibleChange={handleUpdateModalVisible}
          onFinish={async (value) => {
            const success = await handleUpdate(value as API.ServiceUpdateParams);
            if (success) {
              handleUpdateModalVisible(false);
              setCurrentRow(undefined);
              if (actionRef.current) {
                actionRef.current.reload();
              }
            }
          }}
          values={currentRow}
        />
      )}

      {/* 服务详情抽屉 */}
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.name && (
          <ProDescriptions<API.ServiceInfo>
            column={2}
            title={currentRow?.name}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as any}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default ServiceList;
