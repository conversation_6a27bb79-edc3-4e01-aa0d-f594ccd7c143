import React, { useEffect } from 'react';
import { ModalForm, ProFormText, ProFormTextArea, ProFormDigit, ProFormSelect, ProFormSwitch } from '@ant-design/pro-components';

export type ServiceFormProps = {
  title: string;
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  onFinish: (values: Record<string, any>) => Promise<boolean>;
  values?: API.ServiceInfo;
};

/**
 * 服务表单组件
 */
const ServiceForm: React.FC<ServiceFormProps> = (props) => {
  const { title, visible, onVisibleChange, onFinish, values } = props;

  return (
    <ModalForm
      title={title}
      width={640}
      open={visible}
      onOpenChange={onVisibleChange}
      onFinish={onFinish}
      initialValues={values}
    >
      <ProFormText
        name="name"
        label="服务名称"
        placeholder="请输入服务名称"
        rules={[
          {
            required: true,
            message: '请输入服务名称',
          },
        ]}
      />
      <ProFormTextArea
        name="description"
        label="服务描述"
        placeholder="请输入服务描述"
        rules={[
          {
            required: true,
            message: '请输入服务描述',
          },
        ]}
      />
      <ProFormDigit
        name="price"
        label="价格"
        placeholder="请输入价格"
        min={0}
        fieldProps={{
          precision: 2,
          formatter: (value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
          parser: (value) => value!.replace(/\¥\s?|(,*)/g, ''),
        }}
        rules={[
          {
            required: true,
            message: '请输入价格',
          },
        ]}
      />
      <ProFormDigit
        name="duration"
        label="时长（小时）"
        placeholder="请输入服务时长"
        min={0}
        fieldProps={{
          precision: 1,
        }}
        rules={[
          {
            required: true,
            message: '请输入服务时长',
          },
        ]}
      />
      <ProFormSelect
        name="category"
        label="服务类别"
        placeholder="请选择服务类别"
        options={[
          {
            value: '日常保洁',
            label: '日常保洁',
          },
          {
            value: '深度清洁',
            label: '深度清洁',
          },
          {
            value: '专项服务',
            label: '专项服务',
          },
          {
            value: '家电清洗',
            label: '家电清洗',
          },
          {
            value: '其他',
            label: '其他',
          },
        ]}
        rules={[
          {
            required: true,
            message: '请选择服务类别',
          },
        ]}
      />
      <ProFormSwitch
        name="isActive"
        label="状态"
        checkedChildren="启用"
        unCheckedChildren="禁用"
        initialValue={values?.isActive ?? true}
      />
    </ModalForm>
  );
};

export default ServiceForm;
