<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>家政管理系统 API 文档</title>
  <style>
    :root {
      --primary-color: #1890ff;
      --secondary-color: #52c41a;
      --warning-color: #faad14;
      --error-color: #f5222d;
      --text-color: #333;
      --border-color: #eaeaea;
      --bg-color: #f8f9fa;
      --header-bg: #001529;
      --method-get: #61affe;
      --method-post: #49cc90;
      --method-put: #fca130;
      --method-delete: #f93e3e;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.6;
      color: var(--text-color);
      background-color: var(--bg-color);
      padding-bottom: 50px;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    header {
      background-color: var(--header-bg);
      color: white;
      padding: 30px 0;
      margin-bottom: 30px;
    }

    h1, h2, h3, h4 {
      margin-bottom: 15px;
      font-weight: 600;
    }

    h1 {
      font-size: 2.5rem;
    }

    h2 {
      font-size: 1.8rem;
      margin-top: 40px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
    }

    h3 {
      font-size: 1.4rem;
      margin-top: 30px;
    }

    h4 {
      font-size: 1.2rem;
      margin-top: 20px;
    }

    p {
      margin-bottom: 15px;
    }

    code {
      font-family: SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace;
      background-color: #f1f1f1;
      padding: 2px 5px;
      border-radius: 3px;
      font-size: 0.9em;
    }

    pre {
      background-color: #272822;
      color: #f8f8f2;
      padding: 15px;
      border-radius: 5px;
      overflow-x: auto;
      margin-bottom: 20px;
    }

    .endpoint {
      margin-bottom: 30px;
      border: 1px solid var(--border-color);
      border-radius: 5px;
      overflow: hidden;
    }

    .endpoint-header {
      display: flex;
      padding: 15px;
      background-color: white;
      cursor: pointer;
      align-items: center;
      border-bottom: 1px solid var(--border-color);
    }

    .endpoint-body {
      padding: 20px;
      background-color: white;
      display: none;
    }

    .endpoint.active .endpoint-body {
      display: block;
    }

    .method {
      font-weight: bold;
      padding: 5px 10px;
      border-radius: 3px;
      color: white;
      margin-right: 15px;
      min-width: 80px;
      text-align: center;
    }

    .get { background-color: var(--method-get); }
    .post { background-color: var(--method-post); }
    .put { background-color: var(--method-put); }
    .delete { background-color: var(--method-delete); }

    .url {
      font-family: SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace;
      font-weight: 500;
    }

    .auth-tag {
      margin-left: auto;
      background-color: #e8f4fd;
      color: #0969da;
      padding: 3px 8px;
      border-radius: 3px;
      font-size: 0.8rem;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }

    th, td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid var(--border-color);
    }

    th {
      background-color: #f1f1f1;
      font-weight: 600;
    }

    tr:hover {
      background-color: #f9f9f9;
    }

    .section-toggle {
      cursor: pointer;
      display: flex;
      align-items: center;
    }

    .section-toggle::after {
      content: "▼";
      margin-left: 10px;
      font-size: 0.8rem;
      transition: transform 0.3s;
    }

    .section-toggle.collapsed::after {
      transform: rotate(-90deg);
    }

    .section-content {
      transition: max-height 0.3s ease-out;
      overflow: hidden;
      max-height: 5000px;
    }

    .section-content.collapsed {
      max-height: 0;
    }
  </style>
</head>
<body>
  <header>
    <div class="container">
      <h1>家政管理系统 API 文档</h1>
      <p>本文档提供家政管理系统后端 API 的详细说明</p>
    </div>
  </header>

  <div class="container">
    <section id="introduction">
      <h2>API 概述</h2>
      <p>家政管理系统 API 是一个基于 RESTful 设计的接口集合，用于管理家政服务、订单和用户。API 使用 JSON 格式进行数据交换，并使用 JWT 令牌进行身份验证。</p>

      <h3>基础 URL</h3>
      <p>所有 API 请求的基础 URL 为：<code>http://localhost:5000/api</code></p>

      <h3>API 分类</h3>
      <p>API 分为三类：</p>
      <ul>
        <li><strong>管理后台 API</strong>：<code>/api/admin/...</code> - 用于管理后台的接口</li>
        <li><strong>移动端 API</strong>：<code>/api/app/...</code> - 用于移动端应用的接口</li>
        <li><strong>通用 API</strong>：<code>/api/...</code> - 管理后台和移动端共用的接口</li>
      </ul>

      <h3>认证</h3>
      <p>大多数 API 端点需要认证。认证通过 Bearer Token 方式实现：</p>
      <pre>Authorization: Bearer &lt;your_token&gt;</pre>
      <p>获取令牌需要通过登录接口 <code>POST /api/users/login</code> 或微信登录接口获取。</p>

      <h3>权限级别</h3>
      <p>API 有以下几种权限级别：</p>
      <ul>
        <li><strong>Public</strong>：无需认证即可访问</li>
        <li><strong>Private</strong>：需要有效的认证令牌</li>
        <li><strong>Admin</strong>：需要管理员权限</li>
        <li><strong>Manager</strong>：需要管理员或经理权限</li>
        <li><strong>Customer</strong>：客户权限，适用于移动端 API</li>
      </ul>
    </section>

    <section id="users">
      <h2 class="section-toggle">用户管理 API</h2>
      <div class="section-content">
        <div class="endpoint">
          <div class="endpoint-header">
            <span class="method post">POST</span>
            <span class="url">/api/users/login</span>
            <span class="auth-tag">Public</span>
          </div>
          <div class="endpoint-body">
            <p>用户登录并获取认证令牌</p>

            <h4>请求参数</h4>
            <table>
              <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>描述</th>
              </tr>
              <tr>
                <td>email</td>
                <td>String</td>
                <td>是</td>
                <td>用户邮箱</td>
              </tr>
              <tr>
                <td>password</td>
                <td>String</td>
                <td>是</td>
                <td>用户密码</td>
              </tr>
            </table>

            <h4>响应示例</h4>
            <pre>{
  "id": 1,
  "name": "管理员",
  "email": "<EMAIL>",
  "role": "admin",
  "phone": "13800138000",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}</pre>
          </div>
        </div>

        <div class="endpoint">
          <div class="endpoint-header">
            <span class="method get">GET</span>
            <span class="url">/api/users</span>
            <span class="auth-tag">Admin</span>
          </div>
          <div class="endpoint-body">
            <p>获取所有用户列表</p>

            <h4>响应示例</h4>
            <pre>[
  {
    "id": 1,
    "name": "管理员",
    "email": "<EMAIL>",
    "role": "admin",
    "phone": "13800138000"
  },
  {
    "id": 2,
    "name": "员工1",
    "email": "<EMAIL>",
    "role": "staff",
    "phone": "13900139000"
  }
]</pre>
          </div>
        </div>

        <div class="endpoint">
          <div class="endpoint-header">
            <span class="method post">POST</span>
            <span class="url">/api/users</span>
            <span class="auth-tag">Admin</span>
          </div>
          <div class="endpoint-body">
            <p>注册新用户</p>

            <h4>请求参数</h4>
            <table>
              <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>描述</th>
              </tr>
              <tr>
                <td>name</td>
                <td>String</td>
                <td>是</td>
                <td>用户姓名</td>
              </tr>
              <tr>
                <td>email</td>
                <td>String</td>
                <td>是</td>
                <td>用户邮箱</td>
              </tr>
              <tr>
                <td>password</td>
                <td>String</td>
                <td>是</td>
                <td>用户密码</td>
              </tr>
              <tr>
                <td>role</td>
                <td>String</td>
                <td>否</td>
                <td>用户角色 (admin/manager/staff)</td>
              </tr>
              <tr>
                <td>phone</td>
                <td>String</td>
                <td>是</td>
                <td>用户电话</td>
              </tr>
            </table>

            <h4>响应示例</h4>
            <pre>{
  "id": 3,
  "name": "新员工",
  "email": "<EMAIL>",
  "role": "staff",
  "phone": "13700137000",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}</pre>
          </div>
        </div>
      </div>
    </section>

    <section id="services">
      <h2 class="section-toggle">服务管理 API</h2>
      <div class="section-content">
        <div class="endpoint">
          <div class="endpoint-header">
            <span class="method get">GET</span>
            <span class="url">/api/services</span>
            <span class="auth-tag">Public</span>
          </div>
          <div class="endpoint-body">
            <p>获取所有服务列表</p>

            <h4>响应示例</h4>
            <pre>[
  {
    "id": 1,
    "name": "日常保洁",
    "description": "提供日常家居清洁服务",
    "price": 100.00,
    "duration": 2,
    "category": "日常保洁",
    "isActive": true
  },
  {
    "id": 2,
    "name": "深度清洁",
    "description": "提供全面深度清洁服务",
    "price": 300.00,
    "duration": 4,
    "category": "深度清洁",
    "isActive": true
  }
]</pre>
          </div>
        </div>

        <div class="endpoint">
          <div class="endpoint-header">
            <span class="method get">GET</span>
            <span class="url">/api/services/:id</span>
            <span class="auth-tag">Public</span>
          </div>
          <div class="endpoint-body">
            <p>获取单个服务详情</p>

            <h4>路径参数</h4>
            <table>
              <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>描述</th>
              </tr>
              <tr>
                <td>id</td>
                <td>Number</td>
                <td>服务ID</td>
              </tr>
            </table>

            <h4>响应示例</h4>
            <pre>{
  "id": 1,
  "name": "日常保洁",
  "description": "提供日常家居清洁服务",
  "price": 100.00,
  "duration": 2,
  "category": "日常保洁",
  "isActive": true
}</pre>
          </div>
        </div>
      </div>
    </section>

    <section id="wechat">
      <h2 class="section-toggle">微信相关 API</h2>
      <div class="section-content">
        <div class="endpoint">
          <div class="endpoint-header">
            <span class="method post">POST</span>
            <span class="url">/api/wechat/mini-login</span>
            <span class="auth-tag">Public</span>
          </div>
          <div class="endpoint-body">
            <p>微信小程序登录</p>

            <h4>请求参数</h4>
            <table>
              <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>描述</th>
              </tr>
              <tr>
                <td>code</td>
                <td>String</td>
                <td>是</td>
                <td>微信小程序登录时获取的临时code</td>
              </tr>
            </table>

            <h4>响应示例</h4>
            <pre>{
  "id": 1,
  "name": "微信用户_a1b2c3d4",
  "avatar": "https://thirdwx.qlogo.cn/mmopen/...",
  "role": "customer",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "openid": "oXYZ123456789"
}</pre>
          </div>
        </div>

        <div class="endpoint">
          <div class="endpoint-header">
            <span class="method post">POST</span>
            <span class="url">/api/wechat/update-user-info</span>
            <span class="auth-tag">Public</span>
          </div>
          <div class="endpoint-body">
            <p>更新微信用户信息</p>

            <h4>请求参数</h4>
            <table>
              <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>描述</th>
              </tr>
              <tr>
                <td>openid</td>
                <td>String</td>
                <td>是</td>
                <td>微信用户的openid</td>
              </tr>
              <tr>
                <td>userInfo</td>
                <td>Object</td>
                <td>是</td>
                <td>微信用户信息对象</td>
              </tr>
              <tr>
                <td>userInfo.nickName</td>
                <td>String</td>
                <td>是</td>
                <td>用户昵称</td>
              </tr>
              <tr>
                <td>userInfo.avatarUrl</td>
                <td>String</td>
                <td>是</td>
                <td>用户头像URL</td>
              </tr>
              <tr>
                <td>userInfo.gender</td>
                <td>Number</td>
                <td>是</td>
                <td>用户性别(1:男, 2:女, 0:未知)</td>
              </tr>
              <tr>
                <td>userInfo.country</td>
                <td>String</td>
                <td>否</td>
                <td>用户所在国家</td>
              </tr>
              <tr>
                <td>userInfo.province</td>
                <td>String</td>
                <td>否</td>
                <td>用户所在省份</td>
              </tr>
              <tr>
                <td>userInfo.city</td>
                <td>String</td>
                <td>否</td>
                <td>用户所在城市</td>
              </tr>
            </table>

            <h4>响应示例</h4>
            <pre>{
  "id": 1,
  "name": "微信昵称",
  "avatar": "https://thirdwx.qlogo.cn/mmopen/...",
  "role": "customer"
}</pre>
          </div>
        </div>

        <div class="endpoint">
          <div class="endpoint-header">
            <span class="method post">POST</span>
            <span class="url">/api/wechat/oa-login</span>
            <span class="auth-tag">Public</span>
          </div>
          <div class="endpoint-body">
            <p>微信公众号登录</p>

            <h4>请求参数</h4>
            <table>
              <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>描述</th>
              </tr>
              <tr>
                <td>code</td>
                <td>String</td>
                <td>是</td>
                <td>微信公众号授权后获取的临时code</td>
              </tr>
            </table>

            <h4>响应示例</h4>
            <pre>{
  "id": 1,
  "name": "微信昵称",
  "avatar": "https://thirdwx.qlogo.cn/mmopen/...",
  "role": "customer",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "openid": "oXYZ123456789"
}</pre>
          </div>
        </div>

        <div class="endpoint">
          <div class="endpoint-header">
            <span class="method post">POST</span>
            <span class="url">/api/wechat/bind</span>
            <span class="auth-tag">Private</span>
          </div>
          <div class="endpoint-body">
            <p>绑定微信账号</p>

            <h4>请求参数</h4>
            <table>
              <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>描述</th>
              </tr>
              <tr>
                <td>userId</td>
                <td>Number</td>
                <td>是</td>
                <td>用户ID</td>
              </tr>
              <tr>
                <td>openid</td>
                <td>String</td>
                <td>是</td>
                <td>微信用户的openid</td>
              </tr>
              <tr>
                <td>unionid</td>
                <td>String</td>
                <td>否</td>
                <td>微信用户的unionid</td>
              </tr>
            </table>

            <h4>响应示例</h4>
            <pre>{
  "message": "微信账号绑定成功",
  "user": {
    "id": 1,
    "name": "用户名",
    "email": "<EMAIL>",
    "role": "customer"
  }
}</pre>
          </div>
        </div>

        <div class="endpoint">
          <div class="endpoint-header">
            <span class="method post">POST</span>
            <span class="url">/api/wechat/unbind</span>
            <span class="auth-tag">Private</span>
          </div>
          <div class="endpoint-body">
            <p>解绑微信账号</p>

            <h4>请求参数</h4>
            <table>
              <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>描述</th>
              </tr>
              <tr>
                <td>userId</td>
                <td>Number</td>
                <td>是</td>
                <td>用户ID</td>
              </tr>
            </table>

            <h4>响应示例</h4>
            <pre>{
  "message": "微信账号解绑成功",
  "user": {
    "id": 1,
    "name": "用户名",
    "email": "<EMAIL>",
    "role": "customer"
  }
}</pre>
          </div>
        </div>
      </div>
    </section>

    <section id="payment">
      <h2 class="section-toggle">支付相关 API</h2>
      <div class="section-content">
        <div class="endpoint">
          <div class="endpoint-header">
            <span class="method post">POST</span>
            <span class="url">/api/payment/wechat/create</span>
            <span class="auth-tag">Private</span>
          </div>
          <div class="endpoint-body">
            <p>创建微信支付订单</p>

            <h4>请求参数</h4>
            <table>
              <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>描述</th>
              </tr>
              <tr>
                <td>orderId</td>
                <td>Number</td>
                <td>是</td>
                <td>订单ID</td>
              </tr>
              <tr>
                <td>openid</td>
                <td>String</td>
                <td>是</td>
                <td>微信用户的openid（JSAPI支付必填）</td>
              </tr>
              <tr>
                <td>tradeType</td>
                <td>String</td>
                <td>否</td>
                <td>交易类型（JSAPI/NATIVE/APP，默认JSAPI）</td>
              </tr>
            </table>

            <h4>响应示例</h4>
            <pre>{
  "message": "创建支付订单成功",
  "paymentParams": {
    "appId": "wx123456789",
    "timeStamp": "1623456789",
    "nonceStr": "abcdefghijklmnopqrstuvwxyz123456",
    "package": "prepay_id=wx123456789abcdef0",
    "signType": "MD5",
    "paySign": "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456"
  }
}</pre>
          </div>
        </div>

        <div class="endpoint">
          <div class="endpoint-header">
            <span class="method post">POST</span>
            <span class="url">/api/payment/wechat/notify</span>
            <span class="auth-tag">Public</span>
          </div>
          <div class="endpoint-body">
            <p>微信支付通知回调</p>
            <p>注意：此接口由微信支付系统调用，不需要手动调用。</p>

            <h4>请求参数</h4>
            <p>微信支付系统以XML格式发送支付结果通知</p>

            <h4>响应示例</h4>
            <pre>&lt;xml&gt;
  &lt;return_code&gt;&lt;![CDATA[SUCCESS]]&gt;&lt;/return_code&gt;
  &lt;return_msg&gt;&lt;![CDATA[OK]]&gt;&lt;/return_msg&gt;
&lt;/xml&gt;</pre>
          </div>
        </div>

        <div class="endpoint">
          <div class="endpoint-header">
            <span class="method get">GET</span>
            <span class="url">/api/payment/status/:orderId</span>
            <span class="auth-tag">Private</span>
          </div>
          <div class="endpoint-body">
            <p>查询支付订单状态</p>

            <h4>路径参数</h4>
            <table>
              <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>描述</th>
              </tr>
              <tr>
                <td>orderId</td>
                <td>Number</td>
                <td>订单ID</td>
              </tr>
            </table>

            <h4>响应示例</h4>
            <pre>{
  "orderId": "123",
  "paymentStatus": "completed",
  "orderStatus": "confirmed",
  "paymentMethod": "wechat",
  "amount": 100.00,
  "createdAt": "2023-06-01T12:00:00Z",
  "updatedAt": "2023-06-01T12:05:00Z"
}</pre>
          </div>
        </div>

        <div class="endpoint">
          <div class="endpoint-header">
            <span class="method post">POST</span>
            <span class="url">/api/payment/close/:orderId</span>
            <span class="auth-tag">Private</span>
          </div>
          <div class="endpoint-body">
            <p>关闭支付订单</p>

            <h4>路径参数</h4>
            <table>
              <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>描述</th>
              </tr>
              <tr>
                <td>orderId</td>
                <td>Number</td>
                <td>订单ID</td>
              </tr>
            </table>

            <h4>响应示例</h4>
            <pre>{
  "message": "关闭支付订单成功",
  "orderId": "123"
}</pre>
          </div>
        </div>
      </div>
    </section>

    <section id="orders">
      <h2 class="section-toggle">订单管理 API</h2>
      <div class="section-content">
        <div class="endpoint">
          <div class="endpoint-header">
            <span class="method post">POST</span>
            <span class="url">/api/orders</span>
            <span class="auth-tag">Private</span>
          </div>
          <div class="endpoint-body">
            <p>创建新订单</p>

            <h4>请求参数</h4>
            <table>
              <tr>
                <th>参数名</th>
                <th>类型</th>
                <th>必填</th>
                <th>描述</th>
              </tr>
              <tr>
                <td>customer</td>
                <td>Object</td>
                <td>是</td>
                <td>客户信息</td>
              </tr>
              <tr>
                <td>customer.name</td>
                <td>String</td>
                <td>是</td>
                <td>客户姓名</td>
              </tr>
              <tr>
                <td>customer.phone</td>
                <td>String</td>
                <td>是</td>
                <td>客户电话</td>
              </tr>
              <tr>
                <td>customer.address</td>
                <td>String</td>
                <td>是</td>
                <td>客户地址</td>
              </tr>
              <tr>
                <td>serviceId</td>
                <td>Number</td>
                <td>是</td>
                <td>服务ID</td>
              </tr>
              <tr>
                <td>scheduledDate</td>
                <td>Date</td>
                <td>是</td>
                <td>预约日期时间</td>
              </tr>
              <tr>
                <td>notes</td>
                <td>String</td>
                <td>否</td>
                <td>备注信息</td>
              </tr>
            </table>

            <h4>响应示例</h4>
            <pre>{
  "id": 1,
  "customerName": "张三",
  "customerPhone": "13800138000",
  "customerAddress": "北京市朝阳区某小区1号楼1单元101",
  "serviceId": 1,
  "scheduledDate": "2023-06-15T09:00:00.000Z",
  "status": "待确认",
  "totalPrice": 100.00,
  "paymentStatus": "未支付",
  "notes": "请带好清洁工具",
  "service": {
    "id": 1,
    "name": "日常保洁",
    "price": 100.00,
    "duration": 2
  },
  "assignedStaff": null
}</pre>
          </div>
        </div>

        <div class="endpoint">
          <div class="endpoint-header">
            <span class="method get">GET</span>
            <span class="url">/api/orders</span>
            <span class="auth-tag">Manager</span>
          </div>
          <div class="endpoint-body">
            <p>获取所有订单列表</p>

            <h4>响应示例</h4>
            <pre>[
  {
    "id": 1,
    "customerName": "张三",
    "customerPhone": "13800138000",
    "customerAddress": "北京市朝阳区某小区1号楼1单元101",
    "serviceId": 1,
    "scheduledDate": "2023-06-15T09:00:00.000Z",
    "status": "待确认",
    "totalPrice": 100.00,
    "paymentStatus": "未支付",
    "notes": "请带好清洁工具",
    "service": {
      "id": 1,
      "name": "日常保洁",
      "price": 100.00,
      "duration": 2
    },
    "assignedStaff": null
  }
]</pre>
          </div>
        </div>
      </div>
    </section>

    <section id="error-codes">
      <h2 class="section-toggle">错误码说明</h2>
      <div class="section-content">
        <p>API 可能返回以下错误状态码：</p>
        <table>
          <tr>
            <th>状态码</th>
            <th>描述</th>
          </tr>
          <tr>
            <td>200</td>
            <td>请求成功</td>
          </tr>
          <tr>
            <td>201</td>
            <td>创建成功</td>
          </tr>
          <tr>
            <td>400</td>
            <td>请求参数错误</td>
          </tr>
          <tr>
            <td>401</td>
            <td>未授权或授权失败</td>
          </tr>
          <tr>
            <td>404</td>
            <td>资源不存在</td>
          </tr>
          <tr>
            <td>500</td>
            <td>服务器内部错误</td>
          </tr>
        </table>

        <h3>错误响应格式</h3>
        <pre>{
  "message": "错误信息",
  "stack": "错误堆栈信息（仅在开发环境中返回）"
}</pre>
      </div>
    </section>
  </div>

  <script>
    // 展开/折叠端点详情
    document.querySelectorAll('.endpoint-header').forEach(header => {
      header.addEventListener('click', () => {
        const endpoint = header.parentElement;
        endpoint.classList.toggle('active');
      });
    });

    // 展开/折叠部分
    document.querySelectorAll('.section-toggle').forEach(toggle => {
      toggle.addEventListener('click', () => {
        toggle.classList.toggle('collapsed');
        const content = toggle.nextElementSibling;
        content.classList.toggle('collapsed');
      });
    });

    // 默认展开第一个端点
    document.addEventListener('DOMContentLoaded', () => {
      // 默认展开第一个部分的第一个端点
      const firstSection = document.querySelector('.section-content');
      if (firstSection) {
        const firstEndpoint = firstSection.querySelector('.endpoint');
        if (firstEndpoint) {
          firstEndpoint.classList.add('active');
        }
      }
    });
  </script>
</body>
</html>
