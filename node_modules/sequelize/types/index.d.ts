import DataTypes = require('sequelize/lib/data-types');
import Deferrable = require('sequelize/lib/deferrable');
import Op from 'sequelize/lib/operators';
import QueryTypes = require('sequelize/lib/query-types');
import TableHints = require('sequelize/lib/table-hints');
import IndexHints = require('sequelize/lib/index-hints');
import Utils = require('sequelize/lib/utils');

export * from 'sequelize/lib/associations/index';
export * from 'sequelize/lib/data-types';
export * from 'sequelize/lib/errors/index';
export { BaseError as Error } from 'sequelize/lib/errors/index';
export * from 'sequelize/lib/model';
export * from 'sequelize/lib/dialects/abstract/query-interface';
export * from 'sequelize/lib/sequelize';
export * from 'sequelize/lib/transaction';
export { useInflection } from 'sequelize/lib/utils';
export { Validator } from 'sequelize/lib/utils/validator-extras';
export { Utils, QueryTypes, Op, TableHints, IndexHints, DataTypes, Deferrable };

/**
 * Type helper for making certain fields of an object optional. This is helpful
 * for creating the `CreationAttributes` from your `Attributes` for a Model.
 */
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
