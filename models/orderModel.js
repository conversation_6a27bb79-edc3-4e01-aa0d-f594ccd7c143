const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/db');

const Order = sequelize.define('Order', {
  customerName: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: { msg: '请输入客户姓名' }
    }
  },
  customerPhone: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: { msg: '请输入客户电话' }
    }
  },
  customerAddress: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: { msg: '请输入客户地址' }
    }
  },
  scheduledDate: {
    type: DataTypes.DATE,
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('待确认', '已确认', '进行中', '已完成', '已取消'),
    defaultValue: '待确认'
  },
  totalPrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0
  },
  paymentStatus: {
    type: DataTypes.ENUM('未支付', '已支付', '已退款'),
    defaultValue: '未支付'
  },
  notes: {
    type: DataTypes.TEXT
  }
}, {
  tableName: 'DOrder',
  timestamps: true
});

module.exports = Order;
