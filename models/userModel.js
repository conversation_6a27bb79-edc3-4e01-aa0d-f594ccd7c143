const { DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');
const { sequelize } = require('../config/db');

const User = sequelize.define('User', {
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: { msg: '请输入姓名' }
    }
  },
  email: {
    type: DataTypes.STRING,
    allowNull: true, // 允许为空，因为微信登录用户可能没有邮箱
    validate: {
      isEmail: { msg: '请输入有效的邮箱地址' }
    }
  },
  password: {
    type: DataTypes.STRING,
    allowNull: true, // 允许为空，因为微信登录用户可能没有密码
  },
  role: {
    type: DataTypes.ENUM('admin', 'manager', 'staff', 'customer'), // 添加客户角色
    defaultValue: 'customer'
  },
  phone: {
    type: DataTypes.STRING,
    allowNull: true, // 允许为空，因为微信登录用户可能没有电话
  },
  avatar: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '用户头像URL'
  },
  gender: {
    type: DataTypes.ENUM('male', 'female', 'other'),
    allowNull: true,
    comment: '性别'
  },
  wechatOpenId: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '微信OpenID'
  },
  wechatUnionId: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '微信UnionID'
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'banned'),
    defaultValue: 'active',
    comment: '用户状态'
  },
  lastLoginAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '最后登录时间'
  }
}, {
  tableName: 'DUser',
  timestamps: true,
  hooks: {
    beforeCreate: async (user) => {
      if (user.password) {
        const salt = await bcrypt.genSalt(10);
        user.password = await bcrypt.hash(user.password, salt);
      }
    },
    beforeUpdate: async (user) => {
      if (user.changed('password') && user.password) {
        const salt = await bcrypt.genSalt(10);
        user.password = await bcrypt.hash(user.password, salt);
      }
    },
    beforeSave: (user) => {
      // 如果是微信登录用户且没有设置姓名，则设置默认姓名
      if (user.wechatOpenId && (!user.name || user.name.trim() === '')) {
        user.name = `微信用户_${user.wechatOpenId.substring(0, 8)}`;
      }

      // 更新最后登录时间
      if (user.changed('lastLoginAt')) {
        user.lastLoginAt = new Date();
      }
    }
  }
});

// 验证密码
User.prototype.matchPassword = async function(enteredPassword) {
  // 如果用户没有设置密码（例如微信登录用户），则无法通过密码验证
  if (!this.password) {
    return false;
  }

  try {
    return await bcrypt.compare(enteredPassword, this.password);
  } catch (error) {
    console.error('密码比较出错:', error);
    return false;
  }
};

// 检查用户是否通过微信登录
User.prototype.isWechatUser = function() {
  return !!this.wechatOpenId;
};

module.exports = User;
