const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/db');

const Finance = sequelize.define('Finance', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  type: {
    type: DataTypes.ENUM('income', 'expense'),
    allowNull: false,
    comment: '交易类型：收入或支出'
  },
  category: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '交易类别，如：服务收入、工资支出、办公费用等'
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: '金额'
  },
  date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    comment: '交易日期'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '交易描述'
  },
  orderId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'orders',
      key: 'id'
    },
    comment: '关联的订单ID，如果有的话'
  },
  staffId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '关联的员工ID，如果有的话'
  },
  paymentMethod: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '支付方式，如：现金、微信、支付宝等'
  },
  status: {
    type: DataTypes.ENUM('pending', 'completed', 'cancelled'),
    defaultValue: 'completed',
    comment: '交易状态'
  },
  createdAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'finances',
  timestamps: true
});

module.exports = Finance;
