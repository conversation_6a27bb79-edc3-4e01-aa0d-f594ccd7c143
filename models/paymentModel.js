/**
 * 支付模型
 */
const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/db');

const Payment = sequelize.define('Payment', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  orderId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'DOrder',
      key: 'id',
    },
  },
  paymentMethod: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: '支付方式: wechat, alipay, cash, card',
  },
  transactionId: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '第三方支付交易号',
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: '支付金额',
  },
  status: {
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: 'pending',
    comment: '支付状态: pending, completed, failed, cancelled, refunded',
  },
  paymentData: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '支付相关数据，JSON格式',
  },
  paidAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '支付时间',
  },
  refundId: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '退款交易号',
  },
  refundAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '退款金额',
  },
  refundStatus: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '退款状态: pending, completed, failed',
  },
  refundedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '退款时间',
  },
}, {
  tableName: 'DPayment',
  timestamps: true,
  hooks: {
    beforeCreate: (payment) => {
      if (payment.status === 'completed' && !payment.paidAt) {
        payment.paidAt = new Date();
      }
    },
    beforeUpdate: (payment) => {
      if (payment.status === 'completed' && !payment.paidAt) {
        payment.paidAt = new Date();
      }
      if (payment.refundStatus === 'completed' && !payment.refundedAt) {
        payment.refundedAt = new Date();
      }
    },
  },
});

module.exports = Payment;
