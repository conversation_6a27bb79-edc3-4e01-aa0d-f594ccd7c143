const { sequelize } = require('../config/db');
const User = require('./userModel');
const Service = require('./serviceModel');
const Order = require('./orderModel');
const Customer = require('./customerModel');
const Review = require('./reviewModel');
const Schedule = require('./scheduleModel');
const Finance = require('./financeModel');
const Payment = require('./paymentModel');

// 定义关联关系
Order.belongsTo(Service, { foreignKey: 'serviceId', as: 'service' });
Order.belongsTo(User, { foreignKey: 'assignedStaffId', as: 'assignedStaff' });
Order.belongsTo(Customer, { foreignKey: 'customerId', as: 'customer' });

// 评价关联
Review.belongsTo(Order, { foreignKey: 'orderId', as: 'order' });
Review.belongsTo(Customer, { foreignKey: 'customerId', as: 'customer' });
Review.belongsTo(User, { foreignKey: 'staffId', as: 'staff' });

// 排班关联
Schedule.belongsTo(User, { foreignKey: 'staffId', as: 'staff' });
Schedule.belongsTo(Order, { foreignKey: 'orderId', as: 'order' });

// 财务关联
Finance.belongsTo(Order, { foreignKey: 'orderId', as: 'order' });
Finance.belongsTo(User, { foreignKey: 'staffId', as: 'staff' });

// 支付关联
Payment.belongsTo(Order, { foreignKey: 'orderId', as: 'order' });

// 同步所有模型
const syncModels = async () => {
  try {
    console.log('模型同步已禁用，因为遇到了 "Too many keys specified; max 64 keys allowed" 错误');
    // 不执行实际的同步操作
    // await sequelize.sync({ alter: true });

    // 检查是否需要创建初始管理员用户
    try {
      const adminCount = await User.count({ where: { role: 'admin' } });
      if (adminCount === 0) {
        try {
          // 创建默认管理员用户
          await User.create({
            name: '管理员',
            email: '<EMAIL>',
            password: 'password', // 会自动通过钩子加密
            role: 'admin',
            phone: '13800138000'
          });
          console.log('已创建默认管理员用户');

          // 创建第二个管理员用户，使用您提供的邮箱和密码
          await User.create({
            name: '测试管理员',
            email: '<EMAIL>',
            password: '123456', // 会自动通过钩子加密
            role: 'admin',
            phone: '13900000000'
          });
          console.log('已创建测试管理员用户');
        } catch (err) {
          console.error('创建默认管理员用户失败:', err);
          // 继续执行，不要因为这个错误而退出
        }
      }
    } catch (err) {
      console.error('检查管理员用户失败:', err);
      // 继续执行，不要因为这个错误而退出
    }
  } catch (error) {
    console.error('Error synchronizing models:', error);
    // 不要立即退出，而是抛出错误让上层处理
    throw error;
  }
};

module.exports = {
  syncModels,
  User,
  Service,
  Order,
  Customer,
  Review,
  Schedule,
  Finance,
  Payment
};
