const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/db');

const Service = sequelize.define('Service', {
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      notEmpty: { msg: '请输入服务名称' }
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: false,
    validate: {
      notEmpty: { msg: '请输入服务描述' }
    }
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: { args: [0], msg: '价格不能为负数' }
    }
  },
  duration: {
    type: DataTypes.FLOAT,
    allowNull: false,
    defaultValue: 1,
    validate: {
      min: { args: [0.5], msg: '时长不能小于0.5小时' }
    }
  },
  category: {
    type: DataTypes.ENUM('日常保洁', '深度清洁', '专项服务', '家电清洗', '其他'),
    allowNull: false,
    validate: {
      notEmpty: { msg: '请选择服务类别' }
    }
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  }
}, {
  tableName: 'DService',
  timestamps: true
});

module.exports = Service;
