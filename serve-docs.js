/**
 * 简单的 Express 服务器，用于提供 API 文档
 */
const express = require('express');
const path = require('path');
const fs = require('fs');

const app = express();

// API文档路由
app.get('/api-docs', (req, res) => {
  const apiDocsPath = path.join(__dirname, 'api-docs-new.html');
  fs.readFile(apiDocsPath, 'utf8', (err, data) => {
    if (err) {
      console.error('无法读取API文档文件:', err);
      return res.status(500).send('无法加载API文档');
    }
    res.setHeader('Content-Type', 'text/html');
    res.send(data);
  });
});

// 默认路由
app.get('/', (req, res) => {
  res.send('API 文档服务器运行中，请访问 <a href="/api-docs">/api-docs</a> 查看 API 文档');
});

// 启动服务器
const PORT = 5051;
app.listen(PORT, () => {
  console.log(`API 文档服务器运行在端口 ${PORT}`);
  console.log(`请访问 http://localhost:${PORT}/api-docs 查看 API 文档`);
});
