/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import React, {useEffect} from 'react';
import type {PropsWithChildren} from 'react';
import {
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  Touchable,
  TouchableOpacity,
  useColorScheme,
  View,
} from 'react-native';

import {
  Colors,
  DebugInstructions,
  Header,
  LearnMoreLinks,
  ReloadInstructions,
} from 'react-native/Libraries/NewAppScreen';
// import {registerApp, sendAuthRequest, isWechatInstalled, requestPayment} from 'native-wechat';
// import {
//   SCOPE,
//   SCENE,
//   MP_TYPE,
//   init,
//   sendAuthRequest,
//   open,
//   openMiniProgram,
//   pay,
//   shareText,
//   shareImage,
//   shareAudio,
//   shareVideo,
//   sharePage,
//   shareMiniProgram,
// } from '@react-native-hero/wechat'

import { AuthingGuard } from '@authing/rn';
import { appid } from './pages/constants/id';

type SectionProps = PropsWithChildren<{
  title: string;
}>;

function Section({children, title}: SectionProps): React.JSX.Element {
  const isDarkMode = useColorScheme() === 'dark';
  return (
    <View style={styles.sectionContainer}>
      <Text
        style={[
          styles.sectionTitle,
          {
            color: isDarkMode ? Colors.white : Colors.black,
          },
        ]}>
        {title}
      </Text>
      <Text
        style={[
          styles.sectionDescription,
          {
            color: isDarkMode ? Colors.light : Colors.dark,
          },
        ]}>
        {children}
      </Text>
    </View>
  );
}

function App(): React.JSX.Element {
  const isDarkMode = useColorScheme() === 'dark';

  const backgroundStyle = {
    backgroundColor: isDarkMode ? Colors.darker : Colors.lighter,
  };
  useEffect(() => {
//     init({
//   appId: 'wx5cf395c16e836284',
//   // universalLink: 'ios 的 universalLink，注意包含 https://'
// })

    // return registerApp({appid: 'wx5cf395c16e836284', log: true});
  }, []);
  const handleClick = async () => {
    // 微信登录

// 打开微信
// open()
// sendAuthRequest({
//   scope: SCOPE.USER_INFO,
// })
// .then(response => {
//   console.log("response",response)
//   response.data.code
// })
// .catch((error) => {
//   console.log("error",error)
//   // 登录失败
// })

    // const data =
    // await isWechatInstalled();
  // await  sendAuthRequest({scope: 'snsapi_userinfo'}).then((data) => {
    
  //   return data;
  // }).catch((error) => {
    
  //   console.log('error', error);
  // });
  //  await requestPayment({
  //     partnerId: 'string',
  //   prepayId: 'string',
  //   nonceStr: 'string',
  //   timeStamp: 'string',
  //   sign: 'string'
  //   })
    // console.log('data', data);
  };
  	const options = {
		title: '测试',
		forceLogin: true // 将注册和登录合并，当用户不存在的时候为其自动注册
	};
  return (
    <SafeAreaView style={backgroundStyle}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={backgroundStyle.backgroundColor}
      />
      <View style={{width:300,height:1000}}>
       <AuthingGuard appId={'6841038f0e7c9ec77d3319d0'} options={options} onLogin={() => {
        
      }} />
      </View>

    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  sectionContainer: {
    marginTop: 32,
    paddingHorizontal: 24,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '600',
  },
  sectionDescription: {
    marginTop: 8,
    fontSize: 18,
    fontWeight: '400',
  },
  highlight: {
    fontWeight: '700',
  },
});

export default App;
