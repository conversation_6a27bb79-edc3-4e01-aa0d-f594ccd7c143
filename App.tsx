/**
 * 社区化App主入口
 * 包含登录注册功能的完整应用
 *
 * @format
 */

import React, {  useState } from 'react';
import { StatusBar } from 'react-native';
import { AuthProvider } from './src/contexts/AuthContext';
import LoginScreen from './src/screens/LoginScreen';
import RegisterScreen from './src/screens/RegisterScreen';
import HomeScreen from './src/screens/HomeScreen';
import { useAuth } from './src/contexts/AuthContext';

// 简化的导航组件
const SimpleNavigator: React.FC = () => {
  const { user } = useAuth();
  const [currentScreen, setCurrentScreen] = useState<'login' | 'register'>('login');

  if (!user) {
    return <HomeScreen />;
  }

  const navigation = {
    navigate: (screen: 'login' | 'register') => setCurrentScreen(screen),
    goBack: () => setCurrentScreen('login'),
  };
  console.log("currentScreen",currentScreen)

  if (currentScreen === 'register') {
    return <RegisterScreen navigation={navigation} />;
  }

  return <LoginScreen navigation={navigation} />;
};

function App(): JSX.Element {

  return (
    <>
      <StatusBar barStyle="dark-content" backgroundColor="#fff" />
      <AuthProvider>
        <SimpleNavigator />
      </AuthProvider>
    </>
  );
}

export default App;
