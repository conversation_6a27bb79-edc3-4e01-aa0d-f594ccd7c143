const { Sequelize } = require('sequelize');
const dotenv = require('dotenv');

dotenv.config();

// 打印环境变量，用于调试
console.log('数据库连接信息:');
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PORT:', process.env.DB_PORT);
console.log('DB_NAME:', process.env.DB_NAME);
console.log('DB_USER:', process.env.DB_USER);

const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    dialect: 'mysql',
    port: process.env.DB_PORT || 3306,
    logging: false,
    pool: {
      max: 10,         // 增加最大连接数
      min: 2,          // 保持最小连接数
      acquire: 60000,  // 增加获取连接的超时时间
      idle: 30000,     // 增加空闲连接的超时时间
      evict: 30000     // 定期检查空闲连接
    },
    retry: {
      max: 3,          // 连接失败时重试3次
      timeout: 10000   // 重试超时时间
    },
    dialectOptions: {
      connectTimeout: 60000, // 连接超时时间
    }
  }
);

// 添加连接事件监听
sequelize.authenticate()
  .then(() => console.log('MySQL Database Connected'))
  .catch(err => console.error('Unable to connect to the database:', err));

// 定期ping数据库保持连接
setInterval(() => {
  sequelize.query('SELECT 1')
    .then(() => console.log('Database connection is alive'))
    .catch(err => console.error('Error pinging database:', err));
}, 60000); // 每分钟ping一次

const connectDB = async () => {
  try {
    await sequelize.authenticate();
    console.log('MySQL Database Connected');
  } catch (error) {
    console.error(`Error: ${error.message}`);
    // 不要立即退出，而是重试
    console.log('Retrying database connection in 5 seconds...');
    setTimeout(connectDB, 5000);
  }
};

module.exports = { connectDB, sequelize };
