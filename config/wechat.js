/**
 * 微信相关配置
 */
require('dotenv').config();

module.exports = {
  // 微信小程序配置
  miniProgram: {
    appId: process.env.WECHAT_MINI_APP_ID || '',
    appSecret: process.env.WECHAT_MINI_APP_SECRET || '',
  },
  
  // 微信公众号配置
  officialAccount: {
    appId: process.env.WECHAT_OA_APP_ID || '',
    appSecret: process.env.WECHAT_OA_APP_SECRET || '',
    token: process.env.WECHAT_OA_TOKEN || '',
    encodingAESKey: process.env.WECHAT_OA_ENCODING_AES_KEY || '',
  },
  
  // 微信支付配置
  payment: {
    appId: process.env.WECHAT_PAY_APP_ID || '',
    mchId: process.env.WECHAT_PAY_MCH_ID || '',
    apiKey: process.env.WECHAT_PAY_API_KEY || '',
    pfx: process.env.WECHAT_PAY_PFX_PATH || '',
    notifyUrl: process.env.WECHAT_PAY_NOTIFY_URL || 'https://example.com/api/payment/wechat/notify',
    refundNotifyUrl: process.env.WECHAT_PAY_REFUND_NOTIFY_URL || 'https://example.com/api/payment/wechat/refund-notify',
  },
  
  // 微信开放平台配置
  openPlatform: {
    appId: process.env.WECHAT_OPEN_APP_ID || '',
    appSecret: process.env.WECHAT_OPEN_APP_SECRET || '',
  },
  
  // 微信接口地址
  apiUrls: {
    // 小程序接口
    code2Session: 'https://api.weixin.qq.com/sns/jscode2session',
    
    // 公众号接口
    oauth2AccessToken: 'https://api.weixin.qq.com/sns/oauth2/access_token',
    oauth2Refresh: 'https://api.weixin.qq.com/sns/oauth2/refresh_token',
    oauth2UserInfo: 'https://api.weixin.qq.com/sns/userinfo',
    
    // 支付接口
    unifiedOrder: 'https://api.mch.weixin.qq.com/pay/unifiedorder',
    orderQuery: 'https://api.mch.weixin.qq.com/pay/orderquery',
    closeOrder: 'https://api.mch.weixin.qq.com/pay/closeorder',
    refund: 'https://api.mch.weixin.qq.com/secapi/pay/refund',
    refundQuery: 'https://api.mch.weixin.qq.com/pay/refundquery',
  },
};
