/**
 * 支付相关配置
 */
require('dotenv').config();

module.exports = {
  // 微信支付配置
  wechat: {
    appId: process.env.WECHAT_PAY_APP_ID || '',
    mchId: process.env.WECHAT_PAY_MCH_ID || '',
    apiKey: process.env.WECHAT_PAY_API_KEY || '',
    pfx: process.env.WECHAT_PAY_PFX_PATH || '',
    notifyUrl: process.env.WECHAT_PAY_NOTIFY_URL || 'https://example.com/api/payment/wechat/notify',
    refundNotifyUrl: process.env.WECHAT_PAY_REFUND_NOTIFY_URL || 'https://example.com/api/payment/wechat/refund-notify',
  },
  
  // 支付宝配置
  alipay: {
    appId: process.env.ALIPAY_APP_ID || '',
    privateKey: process.env.ALIPAY_PRIVATE_KEY || '',
    publicKey: process.env.ALIPAY_PUBLIC_KEY || '',
    notifyUrl: process.env.ALIPAY_NOTIFY_URL || 'https://example.com/api/payment/alipay/notify',
    returnUrl: process.env.ALIPAY_RETURN_URL || 'https://example.com/payment/result',
  },
  
  // 银联支付配置
  unionpay: {
    merId: process.env.UNIONPAY_MER_ID || '',
    privateKey: process.env.UNIONPAY_PRIVATE_KEY || '',
    publicKey: process.env.UNIONPAY_PUBLIC_KEY || '',
    notifyUrl: process.env.UNIONPAY_NOTIFY_URL || 'https://example.com/api/payment/unionpay/notify',
    returnUrl: process.env.UNIONPAY_RETURN_URL || 'https://example.com/payment/result',
  },
  
  // 通用配置
  common: {
    currency: 'CNY',
    timeoutExpress: '2h', // 订单超时时间
    env: process.env.NODE_ENV || 'development',
  },
};
