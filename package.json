{"name": "new_backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.7", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-async-handler": "^1.2.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.1", "sequelize": "^6.37.7", "xml2js": "^0.6.2"}, "devDependencies": {"nodemon": "^3.1.10"}}