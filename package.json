{"name": "home3", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "build": "npx react-native run-android --variant=release"}, "dependencies": {"@authing/rn": "^2.1.1", "@react-native-hero/wechat": "^0.2.5", "native-wechat": "^1.0.21", "react": "18.2.0", "react-native": "0.74.0", "react-native-gesture-handler": "^2.25.0", "react-native-webview": "^13.14.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.74.81", "@react-native/eslint-config": "0.74.81", "@react-native/metro-config": "0.74.81", "@react-native/typescript-config": "0.74.81", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}