{"name": "home2", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "server": "cd server && npm start", "server:dev": "cd server && npm run dev"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.17.11", "axios": "^1.6.2", "native-wechat": "^1.0.21", "react": "18.2.0", "react-native": "0.71.11", "react-native-amap3d": "^3.2.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native-community/eslint-config": "^3.2.0", "@tsconfig/react-native": "^2.0.2", "@types/jest": "^29.2.1", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.73.10", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "jest": {"preset": "react-native"}}