# 开发指南

## 快速开始

### 1. 一键启动开发环境

```bash
./scripts/start-dev.sh
```

这个脚本会自动：
- 检查并安装依赖
- 创建环境配置文件
- 启动后端服务器
- 显示前端启动命令

### 2. 手动启动

如果你想分别启动前后端：

```bash
# 启动后端
npm run server:dev

# 启动前端（新终端）
npm start
npm run android  # 或 npm run ios
```

## 开发流程

### 1. 后端开发

后端使用Node.js + Express + SQLite，主要文件：

- `server/src/app.js` - 应用入口
- `server/src/routes/auth.js` - 认证路由
- `server/src/utils/database.js` - 数据库操作
- `server/src/utils/sms.js` - 短信服务
- `server/src/utils/auth.js` - 认证工具

#### 添加新的API接口

1. 在 `server/src/routes/` 下创建新的路由文件
2. 在 `server/src/app.js` 中注册路由
3. 使用 `asyncHandler` 包装异步函数
4. 使用 Joi 进行参数验证

示例：
```javascript
// server/src/routes/posts.js
const express = require('express');
const { asyncHandler } = require('../middleware/errorHandler');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

router.get('/', authenticateToken, asyncHandler(async (req, res) => {
  // 获取帖子列表
  res.json({ success: true, data: [] });
}));

module.exports = router;
```

### 2. 前端开发

前端使用React Native + TypeScript，主要目录：

- `src/screens/` - 页面组件
- `src/components/` - 通用组件
- `src/navigation/` - 导航配置
- `src/services/` - API服务
- `src/contexts/` - React Context

#### 添加新页面

1. 在 `src/screens/` 下创建页面组件
2. 在 `src/navigation/` 中添加路由配置
3. 使用 TypeScript 定义类型

示例：
```typescript
// src/screens/PostListScreen.tsx
import React from 'react';
import { View, Text } from 'react-native';

const PostListScreen: React.FC = () => {
  return (
    <View>
      <Text>帖子列表</Text>
    </View>
  );
};

export default PostListScreen;
```

#### 调用API

使用 `src/services/api.ts` 中的 `apiService`：

```typescript
import apiService from '../services/api';

// 在组件中使用
const fetchPosts = async () => {
  try {
    const response = await apiService.get('/posts');
    if (response.data.success) {
      setPosts(response.data.data);
    }
  } catch (error) {
    console.error('获取帖子失败:', error);
  }
};
```

## 测试

### 后端测试

```bash
cd server
npm test
```

### 前端测试

```bash
npm test
```

### API测试

使用curl测试API：

```bash
# 发送验证码
curl -X POST http://localhost:3000/api/auth/send-sms \
  -H "Content-Type: application/json" \
  -d '{"phone":"13800138000","type":"register"}'

# 注册用户
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"phone":"13800138000","code":"123456","nickname":"测试用户"}'
```

## 调试

### 后端调试

1. 查看控制台日志
2. 检查 `server/database.sqlite` 数据库
3. 使用 `console.log` 或调试器

### 前端调试

1. 使用React Native Debugger
2. 查看Metro bundler日志
3. 使用 `console.log` 或 Flipper

### 常见问题

#### 1. 后端启动失败

- 检查端口3000是否被占用
- 检查 `server/.env` 配置文件
- 查看错误日志

#### 2. 前端连接后端失败

- 确认后端服务正在运行
- 检查 `src/services/api.ts` 中的API地址
- Android模拟器使用 `********:3000`
- iOS模拟器使用 `localhost:3000`

#### 3. 验证码收不到

- 开发环境验证码会在后端控制台显示
- 检查手机号格式是否正确
- 确认短信发送接口调用成功

## 部署

### 开发环境部署

已经通过启动脚本自动配置

### 生产环境部署

1. 修改 `server/.env` 配置
2. 配置真实短信服务
3. 使用HTTPS
4. 配置数据库备份
5. 使用PM2或Docker部署

## 代码规范

### TypeScript

- 使用严格模式
- 为所有函数和变量定义类型
- 使用接口定义数据结构

### 命名规范

- 文件名使用PascalCase（组件）或camelCase（工具）
- 组件名使用PascalCase
- 变量和函数名使用camelCase
- 常量使用UPPER_SNAKE_CASE

### Git提交

使用语义化提交信息：

```
feat: 添加用户注册功能
fix: 修复登录验证码问题
docs: 更新API文档
style: 调整登录页面样式
refactor: 重构认证中间件
test: 添加用户注册测试
```
