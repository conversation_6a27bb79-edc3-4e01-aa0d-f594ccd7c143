Language : [🇺🇸](./README.md) | [🇨🇳](./README.zh-CN.md) | [🇷🇺](./README.ru-RU.md) | [🇹🇷](./README.tr-TR.md) | [🇯🇵](./README.ja-JP.md) | [🇫🇷](./README.fr-FR.md) | [🇵🇹](./README.pt-BR.md) | 🇸🇦 |

<h1 align="center">Ant Design Pro</h1>
<div dir="rtl">

<div align="center">

حل UI جاهز لتطبيقات المؤسسات كنموذج معياري لـ React.

[![Node CI](https://github.com/ant-design/ant-design-pro/actions/workflows/ci.yml/badge.svg)](https://github.com/ant-design/ant-design-pro/actions/workflows/ci.yml) [![Preview Deploy](https://github.com/ant-design/ant-design-pro/actions/workflows/preview-deploy.yml/badge.svg)](https://github.com/ant-design/ant-design-pro/actions/workflows/preview-deploy.yml) [![Build With Umi](https://img.shields.io/badge/build%20with-umi-028fe4.svg?style=flat-square)](http://umijs.org/) ![](https://badgen.net/badge/icon/Ant%20Design?icon=https://gw.alipayobjects.com/zos/antfincdn/Pp4WPgVDB3/KDpgvguMpGfqaHPjicRK.svg&label)

![](https://github.com/user-attachments/assets/fde29061-3d9a-4397-8ac2-397b0e033ef5)

</div>

- معاينة: http://preview.pro.ant.design
- الصفحة الرئيسية: http://pro.ant.design
- توثيق: http://pro.ant.design/docs/getting-started
- سجل التغيير: http://pro.ant.design/docs/changelog
- الأسئلة الشائعة: http://pro.ant.design/docs/faq

## 4.0 صدر الآن! 🎉🎉🎉

[الإعلان عن Ant Design Pro 4.0.0](https://medium.com/ant-design/ant-design-pro-v4-is-here-6f23098ae9d9)

## توظيف الترجمة :loudspeaker:

نحن نحتاج مساعدتك: https://github.com/ant-design/ant-design-pro/issues/120

## الميزات

- :bulb: **تايب سكريبت**: جافا سكريبت لتطوير التطبيقات
- :scroll: **الكتل**: بناء الصفحة مع قالب كتلة
- :gem: **تصميم أنيق**: يتبع [مواصفات تصميم النمل](http://ant.design/)
- :triangular_ruler: **القوالب المشتركة**: قوالب نموذجية لتطبيقات المؤسسة
- :rocket: **حالة التطور الفني**: أحدث حزمة تطوير لـ React/umi/dva/antd
- :iphone: **متجاوب**: مصممة لأحجام الشاشات المتغيرة
- :art: **تصميم**: تصميم قابل للتخصيص مع تكوين بسيط
- :globe_with_meridians: **عالمي**: تم بناؤه بواسطة i18n solution
- :gear: **أفضل الممارسات**: سير عمل قوي لجعل الكود سليم
- :1234: **تطوير وهمي**: سهولة استخدام حل التطوير الوهمي
- :white_check_mark: **اختبار واجهة المستخدم**: يمكنك الطيران بأمان مع اختبارات الوحدة و e2e

## القوالب

```
- لوحة القيادة
  - تحليلي
  - مراقب
  - مساحة العمل
- نموذج
  - النموذج الأساسي
  - نموذج الخطوة
  - نموذج متقدم
- القائمة
  - الجدول القياسي
  - القائمة القياسية
  - قائمة البطاقات
  - قائمة البحث (Project/Applications/Article)
- الملف الشخصي
  - ملف شخصي بسيط
  - ملف شخصي متقدم
- الحساب
  - مركز الحساب
  - اعدادات الحساب
- النتيجة
  - نجاح
  - فشل
- استثناء
  - 403
  - 404
  - 500
- المستخدم
  - تسجيل الدخول
  - تسجيل
  - تسجيل النتائج
```

## الإستخدام

### استخدام ال bash

</div>

<div dir="ltr">

```bash
$ mkdir <your-project-name>
$ cd <your-project-name>
$ yarn create umi  # or npm create umi

# Choose ant-design-pro:
 Select the boilerplate type (Use arrow keys)
❯ ant-design-pro  - Create project with an layout-only ant-design-pro boilerplate, use together with umi block.
  app             - Create project with a simple boilerplate, support typescript.
  block           - Create a umi block.
  library         - Create a library with umi.
  plugin          - Create a umi plugin.

$ git init
$ npm install
$ npm start         # visit http://localhost:8000
```

</div>

<div dir="rtl">

## دعم المتصفحات

المتصفحات الحديثة و.

| [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt="Edge" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Edge | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/firefox/firefox_48x48.png" alt="Firefox" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Firefox | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/chrome/chrome_48x48.png" alt="Chrome" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Chrome | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/safari/safari_48x48.png" alt="Safari" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Safari | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/opera/opera_48x48.png" alt="Opera" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Opera |
| --- | --- | --- | --- | --- |
| Edge | last 2 versions | last 2 versions | last 2 versions | last 2 versions |

## المساهمة

نرحب بأي نوع من المساهمات ، وإليك بعض الأمثلة عن كيفية المساهمة في هذا المشروع:

- استخدم Ant Design Pro في عملك اليومي.
- إرسال [القضايا](http://github.com/ant-design/ant-design-pro/issues) للإبلاغ عن مشكل أو لطرح أسئلة
- اقترح [طلبات السحب](http://github.com/ant-design/ant-design-pro/pulls) لتحسين الكود الخاص بنا.

</div>
