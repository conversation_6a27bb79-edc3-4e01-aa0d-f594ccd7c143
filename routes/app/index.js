/**
 * 移动端 App 路由入口
 */
const express = require('express');
const router = express.Router();
const { protect } = require('../../middleware/authMiddleware');

// 导入现有路由
const userRoutes = require('../userRoutes');
const serviceRoutes = require('../serviceRoutes');
const orderRoutes = require('../orderRoutes');
const reviewRoutes = require('../reviewRoutes');

// 注册路由
router.use('/users', userRoutes);
router.use('/services', serviceRoutes);
router.use('/orders', orderRoutes);
router.use('/reviews', reviewRoutes);

module.exports = router;
