const express = require('express');
const router = express.Router();
const {
  createOrder,
  getOrders,
  getMyOrders,
  getOrderById,
  updateOrderStatus,
  updateOrderPayment,
  assignStaffToOrder,
  deleteOrder,
} = require('../controllers/orderController');
const { protect, admin, manager } = require('../middleware/authMiddleware');

router.route('/').post(protect, createOrder).get(protect, manager, getOrders);
router.route('/myorders').get(protect, getMyOrders);
router
  .route('/:id')
  .get(protect, getOrderById)
  .delete(protect, admin, deleteOrder);
router.route('/:id/status').put(protect, updateOrderStatus);
router.route('/:id/pay').put(protect, admin, updateOrderPayment);
router.route('/:id/assign').put(protect, manager, assignStaffToOrder);

module.exports = router;
