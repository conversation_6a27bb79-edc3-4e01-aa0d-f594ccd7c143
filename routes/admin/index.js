/**
 * 管理后台路由入口
 */
const express = require('express');
const router = express.Router();
const { protect, admin } = require('../../middleware/authMiddleware');

// 导入现有路由
// 使用模拟路由替代原有路由
const userRoutes = require('./userRoutes');
const serviceRoutes = require('./serviceRoutes');
const orderRoutes = require('./orderRoutes');
const customerRoutes = require('../customerRoutes');
const financeRoutes = require('../financeRoutes');
const reviewRoutes = require('../reviewRoutes');
const scheduleRoutes = require('../scheduleRoutes');
const statsRoutes = require('../statsRoutes');

// 注册路由
router.use('/users', userRoutes);
router.use('/services', serviceRoutes);
router.use('/orders', orderRoutes);
router.use('/customers', customerRoutes);
router.use('/finances', financeRoutes);
router.use('/reviews', reviewRoutes);
router.use('/schedules', scheduleRoutes);
router.use('/stats', statsRoutes);

module.exports = router;
