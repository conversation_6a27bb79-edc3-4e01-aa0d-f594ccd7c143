/**
 * 用户管理路由
 */
const express = require('express');
const router = express.Router();
const { protect, admin } = require('../../middleware/authMiddleware');

// 模拟用户数据
const mockUsers = [
  {
    id: 1,
    name: '张三',
    email: 'z<PERSON><PERSON>@example.com',
    phone: '13800138001',
    role: 'staff',
    isActive: true,
    createdAt: new Date('2023-01-10T08:30:00Z'),
    updatedAt: new Date('2023-05-15T11:30:00Z')
  },
  {
    id: 2,
    name: '李四',
    email: '<EMAIL>',
    phone: '13800138002',
    role: 'staff',
    isActive: true,
    createdAt: new Date('2023-02-15T10:20:00Z'),
    updatedAt: new Date('2023-05-20T14:45:00Z')
  },
  {
    id: 3,
    name: '王五',
    email: '<EMAIL>',
    phone: '13800138003',
    role: 'staff',
    isActive: true,
    createdAt: new Date('2023-03-05T16:45:00Z'),
    updatedAt: new Date('2023-05-18T09:30:00Z')
  },
  {
    id: 4,
    name: '赵六',
    email: '<EMAIL>',
    phone: '13800138004',
    role: 'staff',
    isActive: false,
    createdAt: new Date('2023-01-20T09:10:00Z'),
    updatedAt: new Date('2023-04-17T11:20:00Z')
  },
  {
    id: 5,
    name: '钱七',
    email: '<EMAIL>',
    phone: '13800138005',
    role: 'admin',
    isActive: true,
    createdAt: new Date('2022-12-14T14:30:00Z'),
    updatedAt: new Date('2023-05-18T16:40:00Z')
  }
];

// @desc    获取所有用户
// @route   GET /api/admin/users
// @access  Private/Admin
router.get('/', protect, async (req, res) => {
  try {
    console.log('获取用户列表，查询参数:', req.query);
    
    // 处理搜索参数
    let filteredUsers = [...mockUsers];
    
    // 按姓名搜索
    if (req.query.name) {
      const nameQuery = req.query.name.toLowerCase();
      filteredUsers = filteredUsers.filter(user => 
        user.name.toLowerCase().includes(nameQuery)
      );
    }
    
    // 按邮箱搜索
    if (req.query.email) {
      const emailQuery = req.query.email.toLowerCase();
      filteredUsers = filteredUsers.filter(user => 
        user.email.toLowerCase().includes(emailQuery)
      );
    }
    
    // 按电话搜索
    if (req.query.phone) {
      filteredUsers = filteredUsers.filter(user => 
        user.phone.includes(req.query.phone)
      );
    }
    
    // 按角色搜索
    if (req.query.role) {
      filteredUsers = filteredUsers.filter(user => 
        user.role === req.query.role
      );
    }
    
    // 按状态搜索
    if (req.query.isActive !== undefined) {
      const isActive = req.query.isActive === 'true';
      filteredUsers = filteredUsers.filter(user => 
        user.isActive === isActive
      );
    }
    
    // 处理分页
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);
    
    // 返回分页结果
    res.json({
      data: paginatedUsers,
      total: filteredUsers.length,
      page,
      limit,
      success: true
    });
  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({ message: '获取用户列表失败', error: error.message });
  }
});

// @desc    获取单个用户
// @route   GET /api/admin/users/:id
// @access  Private/Admin
router.get('/:id', protect, async (req, res) => {
  try {
    const user = mockUsers.find(u => u.id === parseInt(req.params.id));
    
    if (user) {
      res.json(user);
    } else {
      res.status(404).json({ message: '用户不存在' });
    }
  } catch (error) {
    console.error('获取用户详情失败:', error);
    res.status(500).json({ message: '获取用户详情失败', error: error.message });
  }
});

// @desc    创建用户
// @route   POST /api/admin/users
// @access  Private/Admin
router.post('/', protect, admin, async (req, res) => {
  try {
    const { name, email, phone, password, role, isActive } = req.body;
    
    // 检查邮箱是否已存在
    if (mockUsers.some(u => u.email === email)) {
      return res.status(400).json({ message: '邮箱已被注册' });
    }
    
    const newUser = {
      id: mockUsers.length + 1,
      name,
      email,
      phone,
      role: role || 'staff',
      isActive: isActive !== undefined ? isActive : true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    mockUsers.push(newUser);
    
    res.status(201).json(newUser);
  } catch (error) {
    console.error('创建用户失败:', error);
    res.status(500).json({ message: '创建用户失败', error: error.message });
  }
});

// @desc    更新用户
// @route   PUT /api/admin/users/:id
// @access  Private/Admin
router.put('/:id', protect, admin, async (req, res) => {
  try {
    const { name, email, phone, role, isActive } = req.body;
    
    const userIndex = mockUsers.findIndex(u => u.id === parseInt(req.params.id));
    
    if (userIndex === -1) {
      return res.status(404).json({ message: '用户不存在' });
    }
    
    const user = mockUsers[userIndex];
    
    // 检查邮箱是否已被其他用户使用
    if (email && email !== user.email && mockUsers.some(u => u.email === email)) {
      return res.status(400).json({ message: '邮箱已被注册' });
    }
    
    // 更新用户信息
    mockUsers[userIndex] = {
      ...user,
      name: name || user.name,
      email: email || user.email,
      phone: phone || user.phone,
      role: role || user.role,
      isActive: isActive !== undefined ? isActive : user.isActive,
      updatedAt: new Date()
    };
    
    res.json(mockUsers[userIndex]);
  } catch (error) {
    console.error('更新用户失败:', error);
    res.status(500).json({ message: '更新用户失败', error: error.message });
  }
});

// @desc    删除用户
// @route   DELETE /api/admin/users/:id
// @access  Private/Admin
router.delete('/:id', protect, admin, async (req, res) => {
  try {
    const userIndex = mockUsers.findIndex(u => u.id === parseInt(req.params.id));
    
    if (userIndex === -1) {
      return res.status(404).json({ message: '用户不存在' });
    }
    
    mockUsers.splice(userIndex, 1);
    
    res.json({ message: '用户已删除' });
  } catch (error) {
    console.error('删除用户失败:', error);
    res.status(500).json({ message: '删除用户失败', error: error.message });
  }
});

module.exports = router;
