/**
 * 服务管理路由
 */
const express = require('express');
const router = express.Router();
const { protect, admin } = require('../../middleware/authMiddleware');
const { Service } = require('../../models');
const { Op } = require('sequelize');

// 模拟服务数据，当数据库连接失败时使用
const mockServices = [
  {
    id: 1,
    name: '日常家居保洁',
    description: '提供全面的家居清洁服务，包括地面清洁、家具除尘、厨房清洁等',
    price: 100.00,
    duration: 2,
    category: '日常保洁',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 2,
    name: '深度清洁',
    description: '全面深度清洁，包括死角、缝隙等难以清洁的区域',
    price: 300.00,
    duration: 4,
    category: '深度清洁',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 3,
    name: '厨房专项清洁',
    description: '专业厨房清洁，包括油烟机、灶台、橱柜等',
    price: 200.00,
    duration: 3,
    category: '专项服务',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 4,
    name: '卫生间消毒清洁',
    description: '卫生间深度清洁和消毒服务',
    price: 150.00,
    duration: 2,
    category: '专项服务',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 5,
    name: '空调清洗',
    description: '专业空调清洗和消毒服务',
    price: 180.00,
    duration: 1.5,
    category: '家电清洗',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// @desc    获取所有服务
// @route   GET /api/admin/services
// @access  Public (暂时禁用身份验证以便测试)
router.get('/', async (req, res) => {
  try {
    console.log('获取服务列表，查询参数:', req.query);

    // 处理分页
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    try {
      // 尝试从数据库获取数据
      // 构建查询条件
      const whereClause = {};

      // 按名称搜索
      if (req.query.name) {
        whereClause.name = { [Op.like]: `%${req.query.name}%` };
      }

      // 按类别搜索
      if (req.query.category) {
        whereClause.category = req.query.category;
      }

      // 按状态搜索
      if (req.query.isActive !== undefined) {
        whereClause.isActive = req.query.isActive === 'true';
      }

      // 按价格搜索
      if (req.query.price) {
        whereClause.price = parseFloat(req.query.price);
      }

      // 处理价格范围
      if (req.query.minPrice) {
        whereClause.price = { ...whereClause.price, [Op.gte]: parseFloat(req.query.minPrice) };
      }

      if (req.query.maxPrice) {
        whereClause.price = { ...whereClause.price, [Op.lte]: parseFloat(req.query.maxPrice) };
      }

      console.log('查询条件:', whereClause);

      const offset = (page - 1) * limit;

      // 查询服务
      const { count, rows: services } = await Service.findAndCountAll({
        where: Object.keys(whereClause).length > 0 ? whereClause : undefined,
        limit,
        offset,
        order: [['createdAt', 'DESC']]
      });

      console.log('使用数据库查询服务，结果数量:', count);

      // 返回分页结果
      return res.json({
        data: services,
        total: count,
        page,
        limit,
        success: true,
        source: 'database'
      });
    } catch (dbError) {
      // 数据库查询失败，使用模拟数据
      console.error('数据库查询失败，使用模拟数据:', dbError);

      // 处理搜索参数
      let filteredServices = [...mockServices];

      // 按名称搜索
      if (req.query.name) {
        const nameQuery = req.query.name.toLowerCase();
        filteredServices = filteredServices.filter(service =>
          service.name.toLowerCase().includes(nameQuery)
        );
      }

      // 按类别搜索
      if (req.query.category) {
        filteredServices = filteredServices.filter(service =>
          service.category === req.query.category
        );
      }

      // 按状态搜索
      if (req.query.isActive !== undefined) {
        const isActive = req.query.isActive === 'true';
        filteredServices = filteredServices.filter(service =>
          service.isActive === isActive
        );
      }

      // 按价格搜索
      if (req.query.price) {
        const price = parseFloat(req.query.price);
        filteredServices = filteredServices.filter(service =>
          service.price === price
        );
      }

      // 处理分页
      const startIndex = (page - 1) * limit;
      const endIndex = page * limit;

      const paginatedServices = filteredServices.slice(startIndex, endIndex);

      console.log('使用模拟数据查询服务，结果数量:', filteredServices.length);

      // 返回分页结果
      return res.json({
        data: paginatedServices,
        total: filteredServices.length,
        page,
        limit,
        success: true,
        source: 'mock'
      });
    }
  } catch (error) {
    console.error('获取服务列表失败:', error);
    res.status(500).json({ message: '获取服务列表失败', error: error.message });
  }
});

// @desc    获取单个服务
// @route   GET /api/admin/services/:id
// @access  Private/Admin
router.get('/:id', protect, async (req, res) => {
  try {
    try {
      // 尝试从数据库获取数据
      const service = await Service.findByPk(req.params.id);

      if (service) {
        return res.json(service);
      } else {
        return res.status(404).json({ message: '服务不存在' });
      }
    } catch (dbError) {
      // 数据库查询失败，使用模拟数据
      console.error('数据库查询失败，使用模拟数据:', dbError);

      const service = mockServices.find(s => s.id === parseInt(req.params.id));

      if (service) {
        return res.json(service);
      } else {
        return res.status(404).json({ message: '服务不存在' });
      }
    }
  } catch (error) {
    console.error('获取服务详情失败:', error);
    res.status(500).json({ message: '获取服务详情失败', error: error.message });
  }
});

// @desc    创建服务
// @route   POST /api/admin/services
// @access  Public (暂时禁用身份验证以便测试)
router.post('/', async (req, res) => {
  try {
    const { name, description, price, duration, category, isActive } = req.body;

    try {
      // 尝试使用数据库
      // 检查服务名称是否已存在
      const serviceExists = await Service.findOne({ where: { name } });

      if (serviceExists) {
        return res.status(400).json({ message: '服务名称已存在' });
      }

      // 创建新服务
      const newService = await Service.create({
        name,
        description,
        price: parseFloat(price),
        duration: parseFloat(duration),
        category,
        isActive: isActive !== undefined ? isActive : true
      });

      return res.status(201).json(newService);
    } catch (dbError) {
      // 数据库操作失败，使用模拟数据
      console.error('数据库操作失败，使用模拟数据:', dbError);

      // 检查服务名称是否已存在
      if (mockServices.some(s => s.name === name)) {
        return res.status(400).json({ message: '服务名称已存在' });
      }

      const newService = {
        id: mockServices.length + 1,
        name,
        description,
        price: parseFloat(price),
        duration: parseFloat(duration),
        category,
        isActive: isActive !== undefined ? isActive : true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockServices.push(newService);

      return res.status(201).json(newService);
    }
  } catch (error) {
    console.error('创建服务失败:', error);
    res.status(500).json({ message: '创建服务失败', error: error.message });
  }
});

// @desc    更新服务
// @route   PUT /api/admin/services/:id
// @access  Private/Admin
router.put('/:id', protect, admin, async (req, res) => {
  try {
    const { name, description, price, duration, category, isActive } = req.body;

    try {
      // 尝试使用数据库
      // 查找服务
      const service = await Service.findByPk(req.params.id);

      if (!service) {
        return res.status(404).json({ message: '服务不存在' });
      }

      // 如果更改了名称，检查新名称是否已存在
      if (name && name !== service.name) {
        const nameExists = await Service.findOne({ where: { name } });
        if (nameExists) {
          return res.status(400).json({ message: '服务名称已存在' });
        }
      }

      // 更新服务信息
      service.name = name || service.name;
      service.description = description || service.description;
      service.price = price ? parseFloat(price) : service.price;
      service.duration = duration ? parseFloat(duration) : service.duration;
      service.category = category || service.category;
      service.isActive = isActive !== undefined ? isActive : service.isActive;

      // 保存更新
      const updatedService = await service.save();

      return res.json(updatedService);
    } catch (dbError) {
      // 数据库操作失败，使用模拟数据
      console.error('数据库操作失败，使用模拟数据:', dbError);

      const serviceIndex = mockServices.findIndex(s => s.id === parseInt(req.params.id));

      if (serviceIndex === -1) {
        return res.status(404).json({ message: '服务不存在' });
      }

      const service = mockServices[serviceIndex];

      // 如果更改了名称，检查新名称是否已存在
      if (name && name !== service.name) {
        const nameExists = mockServices.some(s => s.name === name);
        if (nameExists) {
          return res.status(400).json({ message: '服务名称已存在' });
        }
      }

      // 更新服务信息
      mockServices[serviceIndex] = {
        ...service,
        name: name || service.name,
        description: description || service.description,
        price: price ? parseFloat(price) : service.price,
        duration: duration ? parseFloat(duration) : service.duration,
        category: category || service.category,
        isActive: isActive !== undefined ? isActive : service.isActive,
        updatedAt: new Date()
      };

      return res.json(mockServices[serviceIndex]);
    }
  } catch (error) {
    console.error('更新服务失败:', error);
    res.status(500).json({ message: '更新服务失败', error: error.message });
  }
});

// @desc    删除服务
// @route   DELETE /api/admin/services/:id
// @access  Private/Admin
router.delete('/:id', protect, admin, async (req, res) => {
  try {
    try {
      // 尝试使用数据库
      // 查找服务
      const service = await Service.findByPk(req.params.id);

      if (!service) {
        return res.status(404).json({ message: '服务不存在' });
      }

      // 删除服务
      await service.destroy();

      return res.json({ message: '服务已删除' });
    } catch (dbError) {
      // 数据库操作失败，使用模拟数据
      console.error('数据库操作失败，使用模拟数据:', dbError);

      const serviceIndex = mockServices.findIndex(s => s.id === parseInt(req.params.id));

      if (serviceIndex === -1) {
        return res.status(404).json({ message: '服务不存在' });
      }

      mockServices.splice(serviceIndex, 1);

      return res.json({ message: '服务已删除' });
    }
  } catch (error) {
    console.error('删除服务失败:', error);
    res.status(500).json({ message: '删除服务失败', error: error.message });
  }
});

module.exports = router;
