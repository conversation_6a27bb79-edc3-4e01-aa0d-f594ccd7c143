/**
 * 订单管理路由
 */
const express = require('express');
const router = express.Router();
const { protect, admin } = require('../../middleware/authMiddleware');

// 模拟订单数据
const mockOrders = [
  {
    id: 1,
    orderNumber: 'ORD20230001',
    customerName: '张三',
    customerPhone: '13800138001',
    serviceName: '日常家居保洁',
    serviceId: 1,
    staffName: '李四',
    staffId: 1,
    status: 'completed',
    scheduledAt: '2023-06-15T09:00:00Z',
    address: '北京市朝阳区建国路88号',
    price: 100.00,
    paymentStatus: 'paid',
    paymentMethod: 'wechat',
    notes: '客户要求带上消毒用品',
    createdAt: new Date('2023-06-10T08:30:00Z'),
    updatedAt: new Date('2023-06-15T11:30:00Z')
  },
  {
    id: 2,
    orderNumber: 'ORD20230002',
    customerName: '李四',
    customerPhone: '13800138002',
    serviceName: '深度清洁',
    serviceId: 2,
    staffName: '王五',
    staffId: 2,
    status: 'pending',
    scheduledAt: '2023-06-20T14:00:00Z',
    address: '上海市浦东新区陆家嘴环路1000号',
    price: 300.00,
    paymentStatus: 'unpaid',
    paymentMethod: null,
    notes: '需要提前半小时联系客户',
    createdAt: new Date('2023-06-15T10:20:00Z'),
    updatedAt: new Date('2023-06-15T10:20:00Z')
  },
  {
    id: 3,
    orderNumber: 'ORD20230003',
    customerName: '王五',
    customerPhone: '13800138003',
    serviceName: '厨房专项清洁',
    serviceId: 3,
    staffName: '赵六',
    staffId: 3,
    status: 'in_progress',
    scheduledAt: '2023-06-18T10:00:00Z',
    address: '广州市天河区天河路385号',
    price: 200.00,
    paymentStatus: 'paid',
    paymentMethod: 'alipay',
    notes: '',
    createdAt: new Date('2023-06-12T16:45:00Z'),
    updatedAt: new Date('2023-06-18T09:30:00Z')
  },
  {
    id: 4,
    orderNumber: 'ORD20230004',
    customerName: '赵六',
    customerPhone: '13800138004',
    serviceName: '卫生间消毒清洁',
    serviceId: 4,
    staffName: '张三',
    staffId: 1,
    status: 'confirmed',
    scheduledAt: '2023-06-25T15:30:00Z',
    address: '深圳市南山区科技园路1号',
    price: 150.00,
    paymentStatus: 'unpaid',
    paymentMethod: null,
    notes: '客户家有宠物，需要注意',
    createdAt: new Date('2023-06-16T09:10:00Z'),
    updatedAt: new Date('2023-06-17T11:20:00Z')
  },
  {
    id: 5,
    orderNumber: 'ORD20230005',
    customerName: '钱七',
    customerPhone: '13800138005',
    serviceName: '空调清洗',
    serviceId: 5,
    staffName: '王五',
    staffId: 2,
    status: 'cancelled',
    scheduledAt: '2023-06-19T13:00:00Z',
    address: '成都市锦江区红星路三段1号',
    price: 180.00,
    paymentStatus: 'refunded',
    paymentMethod: 'wechat',
    notes: '客户临时有事取消',
    createdAt: new Date('2023-06-14T14:30:00Z'),
    updatedAt: new Date('2023-06-18T16:40:00Z')
  }
];

// @desc    获取所有订单
// @route   GET /api/admin/orders
// @access  Private/Admin
router.get('/', protect, async (req, res) => {
  try {
    console.log('获取订单列表，查询参数:', req.query);
    
    // 处理搜索参数
    let filteredOrders = [...mockOrders];
    
    // 按客户名称搜索
    if (req.query.customerName) {
      const nameQuery = req.query.customerName.toLowerCase();
      filteredOrders = filteredOrders.filter(order => 
        order.customerName.toLowerCase().includes(nameQuery)
      );
    }
    
    // 按状态搜索
    if (req.query.status) {
      filteredOrders = filteredOrders.filter(order => 
        order.status === req.query.status
      );
    }
    
    // 按支付状态搜索
    if (req.query.paymentStatus) {
      filteredOrders = filteredOrders.filter(order => 
        order.paymentStatus === req.query.paymentStatus
      );
    }
    
    // 处理分页
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    
    const paginatedOrders = filteredOrders.slice(startIndex, endIndex);
    
    // 返回分页结果
    res.json({
      data: paginatedOrders,
      total: filteredOrders.length,
      page,
      limit,
      success: true
    });
  } catch (error) {
    console.error('获取订单列表失败:', error);
    res.status(500).json({ message: '获取订单列表失败', error: error.message });
  }
});

// @desc    获取单个订单
// @route   GET /api/admin/orders/:id
// @access  Private/Admin
router.get('/:id', protect, async (req, res) => {
  try {
    const order = mockOrders.find(o => o.id === parseInt(req.params.id));
    
    if (order) {
      res.json(order);
    } else {
      res.status(404).json({ message: '订单不存在' });
    }
  } catch (error) {
    console.error('获取订单详情失败:', error);
    res.status(500).json({ message: '获取订单详情失败', error: error.message });
  }
});

// @desc    创建订单
// @route   POST /api/admin/orders
// @access  Private/Admin
router.post('/', protect, admin, async (req, res) => {
  try {
    const { 
      customerName, 
      customerPhone, 
      serviceId, 
      staffId, 
      scheduledAt, 
      address, 
      price, 
      status, 
      paymentStatus, 
      paymentMethod, 
      notes 
    } = req.body;
    
    const newOrder = {
      id: mockOrders.length + 1,
      orderNumber: `ORD${new Date().getFullYear()}${String(mockOrders.length + 1).padStart(4, '0')}`,
      customerName,
      customerPhone,
      serviceName: '服务名称', // 模拟数据，实际应该从服务表中获取
      serviceId,
      staffName: '员工姓名', // 模拟数据，实际应该从用户表中获取
      staffId,
      status: status || 'pending',
      scheduledAt,
      address,
      price: parseFloat(price),
      paymentStatus: paymentStatus || 'unpaid',
      paymentMethod,
      notes,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    mockOrders.push(newOrder);
    
    res.status(201).json(newOrder);
  } catch (error) {
    console.error('创建订单失败:', error);
    res.status(500).json({ message: '创建订单失败', error: error.message });
  }
});

// @desc    更新订单
// @route   PUT /api/admin/orders/:id
// @access  Private/Admin
router.put('/:id', protect, admin, async (req, res) => {
  try {
    const { 
      customerName, 
      customerPhone, 
      serviceId, 
      staffId, 
      scheduledAt, 
      address, 
      price, 
      status, 
      paymentStatus, 
      paymentMethod, 
      notes 
    } = req.body;
    
    const orderIndex = mockOrders.findIndex(o => o.id === parseInt(req.params.id));
    
    if (orderIndex === -1) {
      return res.status(404).json({ message: '订单不存在' });
    }
    
    const order = mockOrders[orderIndex];
    
    // 更新订单信息
    mockOrders[orderIndex] = {
      ...order,
      customerName: customerName || order.customerName,
      customerPhone: customerPhone || order.customerPhone,
      serviceId: serviceId || order.serviceId,
      staffId: staffId || order.staffId,
      scheduledAt: scheduledAt || order.scheduledAt,
      address: address || order.address,
      price: price ? parseFloat(price) : order.price,
      status: status || order.status,
      paymentStatus: paymentStatus || order.paymentStatus,
      paymentMethod: paymentMethod || order.paymentMethod,
      notes: notes !== undefined ? notes : order.notes,
      updatedAt: new Date()
    };
    
    res.json(mockOrders[orderIndex]);
  } catch (error) {
    console.error('更新订单失败:', error);
    res.status(500).json({ message: '更新订单失败', error: error.message });
  }
});

// @desc    删除订单
// @route   DELETE /api/admin/orders/:id
// @access  Private/Admin
router.delete('/:id', protect, admin, async (req, res) => {
  try {
    const orderIndex = mockOrders.findIndex(o => o.id === parseInt(req.params.id));
    
    if (orderIndex === -1) {
      return res.status(404).json({ message: '订单不存在' });
    }
    
    mockOrders.splice(orderIndex, 1);
    
    res.json({ message: '订单已删除' });
  } catch (error) {
    console.error('删除订单失败:', error);
    res.status(500).json({ message: '删除订单失败', error: error.message });
  }
});

module.exports = router;
