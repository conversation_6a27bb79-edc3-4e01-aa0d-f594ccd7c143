const express = require('express');
const router = express.Router();
const { 
  getFinances, 
  getFinanceById, 
  createFinance, 
  updateFinance, 
  deleteFinance,
  getFinanceStats
} = require('../controllers/financeController');
const { protect } = require('../middleware/authMiddleware');

router.route('/')
  .get(protect, getFinances)
  .post(protect, createFinance);

router.route('/:id')
  .get(protect, getFinanceById)
  .put(protect, updateFinance)
  .delete(protect, deleteFinance);

router.route('/stats')
  .get(protect, getFinanceStats);

module.exports = router;
