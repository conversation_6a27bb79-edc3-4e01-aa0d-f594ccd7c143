/**
 * 支付相关路由
 */
const express = require('express');
const router = express.Router();
const paymentController = require('../../controllers/common/paymentController');
const { protect } = require('../../middleware/authMiddleware');

/**
 * @route   POST /api/payment/wechat/create
 * @desc    创建微信支付订单
 * @access  Private
 */
router.post('/wechat/create', protect, paymentController.createWechatPayment);

/**
 * @route   POST /api/payment/wechat/notify
 * @desc    微信支付通知
 * @access  Public
 */
router.post('/wechat/notify', paymentController.handleWechatPayNotify);

/**
 * @route   GET /api/payment/status/:orderId
 * @desc    查询支付订单状态
 * @access  Private
 */
router.get('/status/:orderId', protect, paymentController.queryPaymentStatus);

/**
 * @route   POST /api/payment/close/:orderId
 * @desc    关闭支付订单
 * @access  Private
 */
router.post('/close/:orderId', protect, paymentController.closePayment);

module.exports = router;
