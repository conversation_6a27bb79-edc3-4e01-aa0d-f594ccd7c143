/**
 * 通用路由入口
 */
const express = require('express');
const router = express.Router();

// 导入路由
const wechatRoutes = require('./wechatRoutes');
const paymentRoutes = require('./paymentRoutes');
const authRoutes = require('./authRoutes');
const userRoutes = require('../userRoutes');

// 注册路由
router.use('/wechat', wechatRoutes);
router.use('/payment', paymentRoutes);
router.use('/auth', authRoutes);

// 用户相关路由
router.use('/users', userRoutes);

module.exports = router;
