/**
 * 通用认证路由
 */
const express = require('express');
const router = express.Router();
const { loginUser } = require('../../controllers/userController');

// 登录路由是公开的，不需要认证
router.post('/login', loginUser);

// 添加对GET方法的支持，但提示用户应该使用POST方法
router.get('/login', (req, res) => {
  res.status(400).json({
    message: '登录接口应该使用POST方法，而不是GET方法',
    example: {
      method: 'POST',
      url: '/api/auth/login',
      body: {
        email: '<EMAIL>',
        password: 'yourpassword'
      }
    }
  });
});

module.exports = router;
