/**
 * 微信相关路由
 */
const express = require('express');
const router = express.Router();
const wechatController = require('../../controllers/common/wechatController');
const { protect } = require('../../middleware/authMiddleware');

/**
 * @route   POST /api/wechat/mini-login
 * @desc    微信小程序登录
 * @access  Public
 */
router.post('/mini-login', wechatController.miniProgramLogin);

/**
 * @route   POST /api/wechat/update-user-info
 * @desc    更新微信用户信息
 * @access  Public
 */
router.post('/update-user-info', wechatController.updateUserInfo);

/**
 * @route   POST /api/wechat/oa-login
 * @desc    微信公众号登录
 * @access  Public
 */
router.post('/oa-login', wechatController.officialAccountLogin);

/**
 * @route   POST /api/wechat/bind
 * @desc    绑定微信账号
 * @access  Private
 */
router.post('/bind', protect, wechatController.bindWechat);

/**
 * @route   POST /api/wechat/unbind
 * @desc    解绑微信账号
 * @access  Private
 */
router.post('/unbind', protect, wechatController.unbindWechat);

module.exports = router;
