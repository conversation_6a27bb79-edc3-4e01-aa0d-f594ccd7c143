const express = require('express');
const router = express.Router();
const { 
  getSchedules, 
  getScheduleById, 
  createSchedule, 
  updateSchedule, 
  deleteSchedule,
  getStaffSchedules
} = require('../controllers/scheduleController');
const { protect } = require('../middleware/authMiddleware');

router.route('/')
  .get(protect, getSchedules)
  .post(protect, createSchedule);

router.route('/:id')
  .get(protect, getScheduleById)
  .put(protect, updateSchedule)
  .delete(protect, deleteSchedule);

router.route('/staff/:id')
  .get(protect, getStaffSchedules);

module.exports = router;
