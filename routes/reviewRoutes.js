const express = require('express');
const router = express.Router();
const { 
  getReviews, 
  getReviewById, 
  createReview, 
  updateReview, 
  deleteReview,
  getStaffReviews
} = require('../controllers/reviewController');
const { protect } = require('../middleware/authMiddleware');

router.route('/')
  .get(protect, getReviews)
  .post(protect, createReview);

router.route('/:id')
  .get(protect, getReviewById)
  .put(protect, updateReview)
  .delete(protect, deleteReview);

router.route('/staff/:id')
  .get(protect, getStaffReviews);

module.exports = router;
