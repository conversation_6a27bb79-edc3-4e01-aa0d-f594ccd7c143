const express = require('express');
const router = express.Router();
const {
  getDashboardStats,
  getOrderStats,
  getStaffPerformance
} = require('../controllers/statsController');
const { protect } = require('../middleware/authMiddleware');

// 根路径也返回仪表盘统计数据
router.route('/')
  .get(protect, getDashboardStats);

router.route('/dashboard')
  .get(protect, getDashboardStats);

router.route('/orders')
  .get(protect, getOrderStats);

router.route('/staff-performance')
  .get(protect, getStaffPerformance);

module.exports = router;
