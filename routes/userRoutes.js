const express = require('express');
const router = express.Router();
const {
  loginUser,
  registerUser,
  getUserProfile,
  updateUserProfile,
  updatePassword,
  getUsers,
  deleteUser,
  getUserById,
  updateUser,
} = require('../controllers/userController');
const { protect, admin } = require('../middleware/authMiddleware');

// 注册和获取用户列表需要管理员权限
router.route('/').post(protect, admin, registerUser).get(protect, admin, getUsers);

// 登录路由是公开的，不需要认证
router.post('/login', loginUser);
// 添加对GET方法的支持，但提示用户应该使用POST方法
router.get('/login', (req, res) => {
  res.status(400).json({
    message: '登录接口应该使用POST方法，而不是GET方法',
    example: {
      method: 'POST',
      url: '/api/users/login',
      body: {
        email: '<EMAIL>',
        password: 'yourpassword'
      }
    }
  });
});

// 添加一个特殊路由，用于创建测试管理员用户
router.get('/create-test-admin', async (req, res) => {
  try {
    const bcrypt = require('bcryptjs');
    const User = require('../models/userModel');

    // 检查用户是否已存在
    const existingUser = await User.findOne({ where: { email: '<EMAIL>' } });

    if (existingUser) {
      // 如果用户已存在，重置密码
      console.log('用户已存在，重置密码');

      // 生成新的盐值和哈希密码
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('123456', salt);

      // 直接更新数据库中的密码
      await User.update(
        { password: hashedPassword },
        { where: { email: '<EMAIL>' } }
      );

      return res.json({
        message: '测试管理员用户密码已重置',
        email: '<EMAIL>',
        password: '123456'
      });
    } else {
      // 如果用户不存在，创建新用户
      console.log('创建新的测试管理员用户');

      // 生成盐值和哈希密码
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('123456', salt);

      // 创建用户
      const newUser = await User.create({
        name: '测试管理员',
        email: '<EMAIL>',
        password: hashedPassword, // 直接使用哈希后的密码
        role: 'admin',
        phone: '13900000000'
      });

      return res.json({
        message: '测试管理员用户已创建',
        email: '<EMAIL>',
        password: '123456',
        userId: newUser.id
      });
    }
  } catch (error) {
    console.error('创建测试管理员用户失败:', error);
    return res.status(500).json({
      message: '创建测试管理员用户失败',
      error: error.message
    });
  }
});
router
  .route('/profile')
  .get(protect, getUserProfile)
  .put(protect, updateUserProfile);

// 修改密码路由
router.put('/profile/password', protect, updatePassword);
router
  .route('/:id')
  .delete(protect, admin, deleteUser)
  .get(protect, admin, getUserById)
  .put(protect, admin, updateUser);

module.exports = router;
