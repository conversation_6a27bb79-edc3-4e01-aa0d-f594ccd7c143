name: Preview Build

on:
  pull_request:
    types: [opened, synchronize, reopened]

permissions:
  contents: read

jobs:
  build-preview:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2
        with:
          ref: ${{ github.event.pull_request.head.sha }}

      - name: build
        run: |
          yarn
          yarn add umi-plugin-pro --save
          yarn build

      - name: upload dist artifact
        uses: actions/upload-artifact@v4
        with:
          name: dist
          path: dist/
          retention-days: 5

      - name: Save PR number
        if: ${{ always() }}
        run: echo ${{ github.event.number }} > ./pr-id.txt

      - name: Upload PR number
        if: ${{ always() }}
        uses: actions/upload-artifact@v4
        with:
          name: pr
          path: ./pr-id.txt
