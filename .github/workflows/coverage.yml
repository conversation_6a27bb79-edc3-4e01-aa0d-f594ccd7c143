name: coverage CI

on: [push, pull_request]

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v1
      - name: Use Node.js 16.x
        uses: actions/setup-node@v1
        with:
          node-version: 16.x
      - run: echo ${{github.ref}}
      - run: curl -f https://get.pnpm.io/v6.16.js | node - add --global pnpm@7
      - run: pnpm config set store-dir ~/.pnpm-store
      - run: pnpm install  --strict-peer-dependencies=false
      - run: yarn run test:coverage
        env:
          CI: true
          PROGRESS: none
          NODE_ENV: test
          NODE_OPTIONS: --max_old_space_size=4096
      - run: bash <(curl -s https://codecov.io/bash)
