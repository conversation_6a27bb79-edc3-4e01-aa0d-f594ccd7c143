name: CI

on: [push, pull_request]

permissions:
  contents: read

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        node_version: [20, 16]
        os: [ubuntu-latest, windows-latest, macOS-latest]
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ matrix.node_version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node_version }}
      - run: echo ${{github.ref}}
      - uses: oven-sh/setup-bun@v2
      - run: bun install
      - run: bun run lint
      - run: bun run tsc
      - run: bun run build
        env:
          CI: true
          PROGRESS: none
          NODE_ENV: test
          NODE_OPTIONS: --max_old_space_size=4096
