/**
 * Express 应用配置
 */
const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const { notFound, errorHandler } = require('./middleware/errorMiddleware');

// 导入路由
const adminRoutes = require('./routes/admin');
const appRoutes = require('./routes/app');
const commonRoutes = require('./routes/common');

// 创建 Express 应用
const app = express();

// 中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cors());

// 静态文件
app.use('/uploads', express.static('uploads'));

// API文档路由
app.get('/api-docs', (req, res) => {
  const apiDocsPath = path.join(__dirname, 'api-docs.html');
  fs.readFile(apiDocsPath, 'utf8', (err, data) => {
    if (err) {
      console.error('无法读取API文档文件:', err);
      return res.status(500).send('无法加载API文档');
    }
    res.setHeader('Content-Type', 'text/html');
    res.send(data);
  });
});

// API 路由
app.use('/api/admin', adminRoutes);
app.use('/api/app', appRoutes);
app.use('/api', commonRoutes);

// 错误处理中间件
app.use(notFound);
app.use(errorHandler);

module.exports = app;
