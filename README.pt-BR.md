Language : [🇺🇸](./README.md) | [🇨🇳](./README.zh-CN.md) | [🇷🇺](./README.ru-RU.md) | [🇹🇷](./README.tr-TR.md) | [🇯🇵](./README.ja-JP.md) | [🇫🇷](./README.fr-FR.md) | 🇵🇹 | [🇸🇦](./README.ar-DZ.md)

<h1 align="center">Ant Design Pro</h1>

<div align="center">

Uma solução de UI pronta para aplicações corporativos na forma de um boilerplate React.

[![Node CI](https://github.com/ant-design/ant-design-pro/actions/workflows/ci.yml/badge.svg)](https://github.com/ant-design/ant-design-pro/actions/workflows/ci.yml) [![Preview Deploy](https://github.com/ant-design/ant-design-pro/actions/workflows/preview-deploy.yml/badge.svg)](https://github.com/ant-design/ant-design-pro/actions/workflows/preview-deploy.yml) [![Build With Umi](https://img.shields.io/badge/build%20with-umi-028fe4.svg?style=flat-square)](http://umijs.org/) ![](https://badgen.net/badge/icon/Ant%20Design?icon=https://gw.alipayobjects.com/zos/antfincdn/Pp4WPgVDB3/KDpgvguMpGfqaHPjicRK.svg&label)

![](https://github.com/user-attachments/assets/fde29061-3d9a-4397-8ac2-397b0e033ef5)

</div>

- Prévia: http://preview.pro.ant.design
- Página Inicial: http://pro.ant.design
- Documentação: http://pro.ant.design/docs/getting-started
- Mudanças: http://pro.ant.design/docs/changelog
- FAQ: http://pro.ant.design/docs/faq

## 4.0 Lançado! 🎉🎉🎉

[Anúncio do Ant Design Pro 4.0.0](https://medium.com/ant-design/ant-design-pro-v4-is-here-6f23098ae9d9)

## Recrutamento para tradução :loudspeaker:

Precisamos da sua ajuda: https://github.com/ant-design/ant-design-pro/issues/120

## Recursos

- :bulb: **TypeScript**: Uma linguaguem para escalar aplicações JavaScript
- :scroll: **Blocks**: Crie páginas com block template
- :gem: **Design Elegante**: Segue as [especificações do Ant Design](http://ant.design/)
- :triangular_ruler: **Modelos Comuns**: Modelos comuns para apliações empresariais
- :rocket: **Estado da Arte do Desenvolvimento**: Stack de desenvolvimento mais recente do React/umi/dva/antd
- :iphone: **Responsivo**: Projetado para tamanhos de telas variados
- :art: **Personalização**: Customizável através de uma simples configuração
- :globe_with_meridians: **Internacionalização**: Incluso i18n por padrão
- :gear: **Melhores Práticas**: Fluxo de trabalho sólido para manter seu código saudável
- :1234: **Desenvolvimento de Mock**: Fácil solução para desenvolvimento de mocks
- :white_check_mark: **Testes de UI**: Voe tranquilamente com testes unitários e testes e2e

## Modelos

```
- Painel de Controle
  - Gráficos
  - Monitoramento
  - Areás de Trabalho
- Formulários
  - Formulários Básicos
  - Formulário com Etapas
  - Formulários Avançados
- Listas
  - Tabela Padrão
  - Lista Padrão
  - Lista com Cards
  - Lista com Busca (Projeto/Aplicações/Artigos)
- Perfís
  - Perfil Simples
  - Perfil Avançado
- Conta
  - Detalhes da Conta
  - Configurações da Conta
- Resultados
  - Secesso
  - Falha
- Exceções
  - 403
  - 404
  - 500
- Usuário
  - Login
  - Cadastro
  - Resultado do Cadastro
```

## Uso

### Use o bash

```bash
$ mkdir <your-project-name>
$ cd <your-project-name>
$ yarn create umi  # ou npm create umi

# Escolha ant-design-pro:
 Selecione o tipo do boilerplate (Use as teclas de seta)
❯ ant-design-pro  - Create project with an layout-only ant-design-pro boilerplate, use together with umi block.
  app             - Create project with a simple boilerplate, support typescript.
  block           - Create a umi block.
  library         - Create a library with umi.
  plugin          - Create a umi plugin.

$ git init
$ npm install
$ npm start         # visit http://localhost:8000
```

## Suporte a navegadores

Navegadores modernos .

| [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt="Edge" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Edge | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/firefox/firefox_48x48.png" alt="Firefox" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Firefox | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/chrome/chrome_48x48.png" alt="Chrome" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Chrome | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/safari/safari_48x48.png" alt="Safari" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Safari | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/opera/opera_48x48.png" alt="Opera" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Opera |
| --- | --- | --- | --- | --- |
| Edge | últimas 2 versões | últimas 2 versões | últimas 2 versões | últimas 2 versões |

## Contribuindo

Qualquer tipo de contribuição é bem-vinda, aqui estão alguns exemplos de como você pode contribuir com esse projeto:

- Use Ant Design Pro no seu trabalho diário.
- Submeta [issues](http://github.com/ant-design/ant-design-pro/issues) para reportar bugs ou tirar dúvidas.
- Proponha [pull requests](http://github.com/ant-design/ant-design-pro/pulls) para melhorar nosso código.
