# Home2 社区化App

这是一个全栈的社区化移动应用，包含完整的用户认证系统，支持手机验证码登录和密码登录。

## 项目结构

```
home2/
├── src/                    # React Native前端源码
│   ├── components/         # 通用组件
│   ├── screens/           # 页面组件
│   ├── navigation/        # 导航配置
│   ├── contexts/          # React Context
│   ├── services/          # API服务
│   ├── types/             # TypeScript类型定义
│   └── utils/             # 工具函数
├── server/                # Node.js后端
│   ├── src/               # 后端源码
│   │   ├── routes/        # API路由
│   │   ├── middleware/    # 中间件
│   │   └── utils/         # 工具函数
│   ├── .env               # 环境配置
│   └── package.json       # 后端依赖
├── android/               # Android原生代码
├── ios/                   # iOS原生代码
└── package.json           # 前端依赖
```

## 功能特性

### 用户认证
- ✅ 手机号注册
- ✅ 手机验证码登录
- ✅ 密码登录（可选）
- ✅ JWT Token认证
- ✅ 用户信息管理
- ✅ 安全登出

### 技术栈

#### 前端 (React Native)
- React Native 0.71.11
- TypeScript
- React Navigation 6
- Axios (HTTP客户端)
- AsyncStorage (本地存储)

#### 后端 (Node.js)
- Express.js
- SQLite数据库
- JWT认证
- bcryptjs密码加密
- 短信验证码服务
- 请求限流和安全防护

## 快速开始

### 1. 安装依赖

```bash
# 安装前端依赖
npm install

# 安装后端依赖
cd server
npm install
cd ..
```

### 2. 启动后端服务

```bash
# 启动后端开发服务器
npm run server:dev
```

后端服务将在 http://localhost:3000 启动

### 3. 启动前端应用

```bash
# 启动Metro bundler
npm start

# 在另一个终端启动Android应用
npm run android

# 或启动iOS应用
npm run ios
```

## API文档

### 认证相关接口

#### 发送短信验证码
```
POST /api/auth/send-sms
Content-Type: application/json

{
  "phone": "13800138000",
  "type": "login" | "register"
}
```

#### 验证码登录
```
POST /api/auth/login-sms
Content-Type: application/json

{
  "phone": "13800138000",
  "code": "123456",
  "deviceInfo": "iOS 16.0"
}
```

#### 密码登录
```
POST /api/auth/login-password
Content-Type: application/json

{
  "phone": "13800138000",
  "password": "password123",
  "deviceInfo": "iOS 16.0"
}
```

#### 用户注册
```
POST /api/auth/register
Content-Type: application/json

{
  "phone": "13800138000",
  "code": "123456",
  "nickname": "用户昵称",
  "password": "password123",  // 可选
  "deviceInfo": "iOS 16.0"
}
```

#### 获取用户信息
```
GET /api/auth/profile
Authorization: Bearer <token>
```

#### 登出
```
POST /api/auth/logout
Authorization: Bearer <token>
```

## 环境配置

### 后端环境变量

复制 `server/.env.example` 到 `server/.env` 并根据需要修改配置：

```bash
# 服务器配置
PORT=3000
NODE_ENV=development

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# 数据库配置
DB_PATH=./database.sqlite

# 短信服务配置
SMS_PROVIDER=mock  # 开发环境使用模拟短信服务
```

## 开发说明

### 短信验证码

开发环境使用模拟短信服务，验证码会在后端控制台输出。生产环境需要配置真实的短信服务商（如阿里云短信服务）。

### 数据库

使用SQLite作为轻量级数据库，数据库文件会自动创建在 `server/database.sqlite`。

### 安全特性

- JWT Token认证
- 密码bcrypt加密
- 请求频率限制
- SQL注入防护
- XSS防护

## 部署

### 后端部署

1. 设置生产环境变量
2. 配置真实短信服务
3. 使用PM2或Docker部署

### 前端部署

1. 修改API地址为生产环境
2. 构建Android/iOS应用
3. 发布到应用商店

## 许可证

MIT License
