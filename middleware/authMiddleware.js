const jwt = require('jsonwebtoken');
const { User } = require('../models');

const protect = async (req, res, next) => {
  // 打印请求路径，用于调试
  console.log(`请求路径: ${req.method} ${req.originalUrl}`);
  console.log('请求头:', JSON.stringify(req.headers));

  // 如果是登录路由，直接放行（支持POST和GET方法）
  if (req.originalUrl.endsWith('/api/users/login')) {
    console.log('登录路由，跳过认证');
    return next();
  }

  let token;

  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith('Bearer')
  ) {
    try {
      // 获取token
      token = req.headers.authorization.split(' ')[1];
      console.log('获取到token:', token);

      // 验证token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      console.log('token验证成功:', decoded);

      // 获取用户信息
      req.user = await User.findByPk(decoded.id, {
        attributes: { exclude: ['password'] }
      });

      if (!req.user) {
        console.log('找不到用户');
        res.status(401);
        return res.json({
          message: '未授权，用户不存在',
        });
      }

      console.log('用户已认证:', req.user.id);
      next();
    } catch (error) {
      console.error('Token验证失败:', error);
      res.status(401);
      res.json({
        message: '未授权，token失效',
        stack: process.env.NODE_ENV === 'production' ? null : error.stack,
      });
    }
  } else {
    console.log('没有提供token');
    res.status(401);
    res.json({
      message: '未授权，没有token',
    });
  }
};

const admin = (req, res, next) => {
  if (req.user && req.user.role === 'admin') {
    next();
  } else {
    res.status(401);
    res.json({
      message: '未授权，需要管理员权限',
    });
  }
};

const manager = (req, res, next) => {
  if (req.user && (req.user.role === 'admin' || req.user.role === 'manager')) {
    next();
  } else {
    res.status(401);
    res.json({
      message: '未授权，需要管理员或经理权限',
    });
  }
};

module.exports = { protect, admin, manager };
