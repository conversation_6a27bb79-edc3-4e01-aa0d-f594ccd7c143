Language : [🇺🇸](./README.md) | [🇨🇳](./README.zh-CN.md) | [🇷🇺](./README.ru-RU.md) | [🇹🇷](./README.tr-TR.md) | 🇯🇵 | [🇫🇷](./README.fr-FR.md) | [🇵🇹](./README.pt-BR.md) | [🇸🇦](./README.ar-DZ.md)

<h1 align="center">Ant Design Pro</h1>

<div align="center">

独創的な業務システムの UI を解決するための React ボイラープレート。

[![Node CI](https://github.com/ant-design/ant-design-pro/actions/workflows/ci.yml/badge.svg)](https://github.com/ant-design/ant-design-pro/actions/workflows/ci.yml) [![Preview Deploy](https://github.com/ant-design/ant-design-pro/actions/workflows/preview-deploy.yml/badge.svg)](https://github.com/ant-design/ant-design-pro/actions/workflows/preview-deploy.yml) [![Build With Umi](https://img.shields.io/badge/build%20with-umi-028fe4.svg?style=flat-square)](http://umijs.org/) ![](https://badgen.net/badge/icon/Ant%20Design?icon=https://gw.alipayobjects.com/zos/antfincdn/Pp4WPgVDB3/KDpgvguMpGfqaHPjicRK.svg&label)

![](https://github.com/user-attachments/assets/fde29061-3d9a-4397-8ac2-397b0e033ef5)

</div>

- プレビュー: http://preview.pro.ant.design
- ホームページ: http://pro.ant.design
- ドキュメント: http://pro.ant.design/docs/getting-started
- 変更ログ: http://pro.ant.design/docs/changelog
- FAQ: http://pro.ant.design/docs/faq

## 2.0 がリリースされました 🎉🎉🎉

[Announcing Ant Design Pro 2.0.0](https://medium.com/ant-design/beautiful-and-powerful-ant-design-pro-2-0-release-51358da5af95)

## 翻訳の募集 :loudspeaker:

私たちはあなたの助けを必要としています。: https://github.com/ant-design/ant-design-pro/issues/120

## 特徴

- :gem: **きちんとしたデザイン**: [Ant Design specification](http://ant.design/) に従ってくださ い。
- :triangular_ruler: **共通のテンプレート**: 業務システム用のテンプレート
- :rocket: **現状のアート開発**: `React/umi/dva/antd` の最新開発スタック
- :iphone: **レスポンシブ**: さまざまな画面サイズ用の設計
- :art: **テーマ**: シンプルな設定でカスタマイズ可能なテーマ
- :globe_with_meridians: **国際化**: 国際化の解決策を内蔵
- :gear: **ベストプラクティス**: コードを美しくするための正しいワークフロー
- :1234: **モック開発**: 使いやすいモック開発
- :white_check_mark: **UI テスト**: ユニットテストと e2e テスト

## テンプレート

```
- ダッシュボード
  - アナリティクス
  - モニター
  - ワークスペース
- 形
  - 基本フォーム
  - ステップフォーム
  - 高度なフォーム
 - リスト
  - スタンダードテーブル
  - スタンダードリスト
  - カードリスト
  - 検索リスト（プロジェクト/アプリケーション/記事）
 - プロフィール
  - 簡単なプロフィール
  - 高度なプロファイル
 - アカウント
  - アカウントセンター
  - アカウント設定
 - 結果
  - 成功
  - 失敗
 - 例外
  - 403
  - 404
  - 500
 - ユーザー
  - ログイン
  - 登録
  - 登録結果
```

## 使用法

### bash を使う方法

```bash
$ mkdir <your-project-name>
$ cd <your-project-name>
$ yarn create umi  # or npm create umi

# Choose ant-design-pro:
 Select the boilerplate type (Use arrow keys)
❯ ant-design-pro  - Create project with an layout-only ant-design-pro boilerplate, use together with umi block.
  app             - Create project with a simple boilerplate, support typescript.
  block           - Create a umi block.
  library         - Create a library with umi.
  plugin          - Create a umi plugin.

$ git init
$ npm install
$ npm start         # http://localhost:8000 を開く
```

## サポートするブラウザー

モダンなブラウザと。

| [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt="Edge" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Edge | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/firefox/firefox_48x48.png" alt="Firefox" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Firefox | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/chrome/chrome_48x48.png" alt="Chrome" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Chrome | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/safari/safari_48x48.png" alt="Safari" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Safari | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/opera/opera_48x48.png" alt="Opera" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Opera |
| --- | --- | --- | --- | --- |
| Edge | 最新版から 2 バージョン | 最新版から 2 バージョン | 最新版から 2 バージョン | 最新版から 2 バージョン |

## 貢献する

どんな種類の貢献でも大歓迎です。あなたがこのプロジェクトに貢献できる方法のいくつかの例はここにあります。：

- 毎日の仕事に Ant Design Pro を使用すること。
- 報告すること。 [issues](http://github.com/ant-design/ant-design-pro/issues) にバグ報告や質問をしてください。
- 更新すること。 改善を、[pull requests](http://github.com/ant-design/ant-design-pro/pulls) で送ってください。

[![DevDependencies](https://img.shields.io/david/dev/ant-design/ant-design-pro.svg)](https://david-dm.org/ant-design/ant-design-pro?type=dev)
