/**
 * 初始化服务数据
 * 
 * 这个脚本用于向数据库中添加初始的服务数据
 * 可以通过 node scripts/initServices.js 运行
 */

const { Service } = require('../models');
const { sequelize } = require('../config/db');

// 初始服务数据
const initialServices = [
  {
    name: '日常家居保洁',
    description: '提供全面的家居清洁服务，包括地面清洁、家具除尘、厨房清洁等',
    price: 100.00,
    duration: 2,
    category: '日常保洁',
    isActive: true
  },
  {
    name: '深度清洁',
    description: '全面深度清洁，包括死角、缝隙等难以清洁的区域',
    price: 300.00,
    duration: 4,
    category: '深度清洁',
    isActive: true
  },
  {
    name: '厨房专项清洁',
    description: '专业厨房清洁，包括油烟机、灶台、橱柜等',
    price: 200.00,
    duration: 3,
    category: '专项服务',
    isActive: true
  },
  {
    name: '卫生间消毒清洁',
    description: '卫生间深度清洁和消毒服务',
    price: 150.00,
    duration: 2,
    category: '专项服务',
    isActive: true
  },
  {
    name: '空调清洗',
    description: '专业空调清洗和消毒服务',
    price: 180.00,
    duration: 1.5,
    category: '家电清洗',
    isActive: true
  }
];

// 初始化服务数据
const initServices = async () => {
  try {
    // 连接数据库
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 同步服务模型
    await Service.sync({ alter: true });
    console.log('服务模型同步成功');

    // 检查是否已有服务数据
    const serviceCount = await Service.count();
    if (serviceCount > 0) {
      console.log(`数据库中已有 ${serviceCount} 条服务数据，跳过初始化`);
      return;
    }

    // 批量创建服务
    const services = await Service.bulkCreate(initialServices);
    console.log(`成功创建 ${services.length} 条服务数据`);

    // 关闭数据库连接
    await sequelize.close();
    console.log('数据库连接已关闭');
  } catch (error) {
    console.error('初始化服务数据失败:', error);
  }
};

// 如果直接运行脚本，则执行初始化
if (require.main === module) {
  initServices();
}

module.exports = initServices;
