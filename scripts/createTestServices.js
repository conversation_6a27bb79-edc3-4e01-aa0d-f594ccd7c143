/**
 * 创建测试服务数据脚本
 */
const { Service } = require('../models');
const { sequelize } = require('../config/db');

const createTestServices = async () => {
  try {
    // 直接使用原始SQL查询检查服务表是否存在
    const [results] = await sequelize.query(`
      SELECT COUNT(*) as count
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'DService'
    `);

    const tableExists = results[0]?.count > 0;

    if (!tableExists) {
      console.log('服务表不存在，跳过创建测试数据');
      return;
    }

    // 检查是否已有服务数据
    const [serviceResults] = await sequelize.query('SELECT COUNT(*) as count FROM DService');
    const serviceCount = serviceResults[0]?.count || 0;

    if (serviceCount === 0) {
      console.log('开始创建测试服务数据...');

      // 创建测试服务数据
      const testServices = [
        {
          name: '日常家居保洁',
          description: '提供全面的家居清洁服务，包括地面清洁、家具除尘、厨房清洁等',
          price: 100.00,
          duration: 2,
          category: '日常保洁',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          name: '深度清洁',
          description: '全面深度清洁，包括死角、缝隙等难以清洁的区域',
          price: 300.00,
          duration: 4,
          category: '深度清洁',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          name: '厨房专项清洁',
          description: '专业厨房清洁，包括油烟机、灶台、橱柜等',
          price: 200.00,
          duration: 3,
          category: '专项服务',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          name: '卫生间消毒清洁',
          description: '卫生间深度清洁和消毒服务',
          price: 150.00,
          duration: 2,
          category: '专项服务',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          name: '空调清洗',
          description: '专业空调清洗和消毒服务',
          price: 180.00,
          duration: 1.5,
          category: '家电清洗',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      // 使用原始SQL插入数据
      for (const service of testServices) {
        await sequelize.query(`
          INSERT INTO DService (name, description, price, duration, category, isActive, createdAt, updatedAt)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, {
          replacements: [
            service.name,
            service.description,
            service.price,
            service.duration,
            service.category,
            service.isActive ? 1 : 0,
            service.createdAt,
            service.updatedAt
          ]
        });
      }

      console.log('测试服务数据创建成功！');
    } else {
      console.log(`已存在 ${serviceCount} 条服务数据，跳过创建测试数据。`);
    }
  } catch (error) {
    console.error('创建测试服务数据失败:', error);
    throw error; // 重新抛出错误，让调用者处理
  }
};

// 如果直接运行此脚本，则执行创建测试数据
if (require.main === module) {
  createTestServices()
    .then(() => {
      console.log('脚本执行完成');
      process.exit(0);
    })
    .catch(err => {
      console.error('脚本执行失败:', err);
      process.exit(1);
    });
} else {
  // 作为模块导出
  module.exports = createTestServices;
}
