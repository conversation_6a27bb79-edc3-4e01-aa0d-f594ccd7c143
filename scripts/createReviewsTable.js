const { sequelize } = require('../config/db');

async function createReviewsTable() {
  try {
    console.log('开始创建 reviews 表...');

    // 创建 reviews 表的 SQL 语句
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS reviews (
        id INT AUTO_INCREMENT PRIMARY KEY,
        orderId INT NOT NULL,
        customerId INT NOT NULL,
        staffId INT NOT NULL,
        rating INT NOT NULL,
        comment TEXT,
        serviceQuality INT,
        attitude INT,
        punctuality INT,
        status ENUM('published', 'hidden') DEFAULT 'published',
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `;

    // 执行 SQL 语句
    await sequelize.query(createTableSQL);

    console.log('reviews 表创建成功！');
  } catch (error) {
    console.error('创建 reviews 表时出错:', error);
  } finally {
    // 关闭连接
    await sequelize.close();
  }
}

// 执行函数
createReviewsTable();
