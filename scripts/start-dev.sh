#!/bin/bash

# 社区化App开发环境启动脚本

echo "🚀 启动Home2社区化App开发环境..."

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请先安装Node.js"
    exit 1
fi

# 检查依赖是否安装
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
fi

if [ ! -d "server/node_modules" ]; then
    echo "📦 安装后端依赖..."
    cd server && npm install && cd ..
fi

# 检查后端环境配置
if [ ! -f "server/.env" ]; then
    echo "⚙️  创建后端环境配置..."
    cp server/.env.example server/.env
fi

# 启动后端服务器
echo "🔧 启动后端服务器..."
cd server
npm run dev &
BACKEND_PID=$!
cd ..

# 等待后端启动
echo "⏳ 等待后端服务启动..."
sleep 3

# 检查后端是否启动成功
if curl -s http://localhost:3000/health > /dev/null; then
    echo "✅ 后端服务启动成功 (http://localhost:3000)"
else
    echo "❌ 后端服务启动失败"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

echo ""
echo "🎉 开发环境启动完成！"
echo ""
echo "📱 前端启动命令："
echo "   npm start          # 启动Metro bundler"
echo "   npm run android    # 启动Android应用"
echo "   npm run ios        # 启动iOS应用"
echo ""
echo "🔧 后端服务："
echo "   地址: http://localhost:3000"
echo "   健康检查: http://localhost:3000/health"
echo "   API文档: 查看README.md"
echo ""
echo "📝 测试账号："
echo "   手机号: 13800138000"
echo "   密码: test123456"
echo ""
echo "💡 提示："
echo "   - 验证码会在后端控制台显示"
echo "   - 数据库文件: server/database.sqlite"
echo "   - 按Ctrl+C停止后端服务"
echo ""

# 保持脚本运行，等待用户中断
wait $BACKEND_PID
