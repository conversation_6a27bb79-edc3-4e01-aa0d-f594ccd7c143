<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>家政管理系统 API 文档</title>
  <style>
    :root {
      --primary-color: #1890ff;
      --secondary-color: #52c41a;
      --warning-color: #faad14;
      --error-color: #f5222d;
      --text-color: #333;
      --border-color: #eaeaea;
      --bg-color: #f8f9fa;
      --header-bg: #001529;
      --method-get: #61affe;
      --method-post: #49cc90;
      --method-put: #fca130;
      --method-delete: #f93e3e;
      --tab-active-bg: #1890ff;
      --tab-active-color: white;
      --tab-hover-bg: #e6f7ff;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.6;
      color: var(--text-color);
      background-color: var(--bg-color);
      padding-bottom: 50px;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    header {
      background-color: var(--header-bg);
      color: white;
      padding: 30px 0 0 0;
      margin-bottom: 30px;
    }

    .header-content {
      padding-bottom: 20px;
    }

    h1, h2, h3, h4 {
      margin-bottom: 15px;
      font-weight: 600;
    }

    h1 {
      font-size: 2.5rem;
    }

    h2 {
      font-size: 1.8rem;
      margin-top: 40px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
    }

    h3 {
      font-size: 1.4rem;
      margin-top: 30px;
    }

    h4 {
      font-size: 1.2rem;
      margin-top: 20px;
    }

    p {
      margin-bottom: 15px;
    }

    code {
      font-family: SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace;
      background-color: #f1f1f1;
      padding: 2px 5px;
      border-radius: 3px;
      font-size: 0.9em;
    }

    pre {
      background-color: #272822;
      color: #f8f8f2;
      padding: 15px;
      border-radius: 5px;
      overflow-x: auto;
      margin-bottom: 20px;
    }

    .endpoint {
      margin-bottom: 30px;
      border: 1px solid var(--border-color);
      border-radius: 5px;
      overflow: hidden;
    }

    .endpoint-header {
      display: flex;
      padding: 15px;
      background-color: white;
      cursor: pointer;
      align-items: center;
      border-bottom: 1px solid var(--border-color);
    }

    .endpoint-body {
      padding: 20px;
      background-color: white;
      display: none;
    }

    .endpoint.active .endpoint-body {
      display: block;
    }

    .method {
      font-weight: bold;
      padding: 5px 10px;
      border-radius: 3px;
      color: white;
      margin-right: 15px;
      min-width: 80px;
      text-align: center;
    }

    .get { background-color: var(--method-get); }
    .post { background-color: var(--method-post); }
    .put { background-color: var(--method-put); }
    .delete { background-color: var(--method-delete); }

    .url {
      font-family: SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace;
      font-weight: 500;
    }

    .auth-tag {
      margin-left: auto;
      background-color: #e8f4fd;
      color: #0969da;
      padding: 3px 8px;
      border-radius: 3px;
      font-size: 0.8rem;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }

    th, td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid var(--border-color);
    }

    th {
      background-color: #f1f1f1;
      font-weight: 600;
    }

    tr:hover {
      background-color: #f9f9f9;
    }

    .section-toggle {
      cursor: pointer;
      display: flex;
      align-items: center;
    }

    .section-toggle::after {
      content: "▼";
      margin-left: 10px;
      font-size: 0.8rem;
      transition: transform 0.3s;
    }

    .section-toggle.collapsed::after {
      transform: rotate(-90deg);
    }

    .section-content {
      transition: max-height 0.3s ease-out;
      overflow: hidden;
      max-height: 5000px;
    }

    .section-content.collapsed {
      max-height: 0;
    }

    /* 标签栏样式 */
    .tabs {
      display: flex;
      background-color: #fff;
      border-bottom: 1px solid var(--border-color);
      overflow-x: auto;
      white-space: nowrap;
    }

    .tab {
      padding: 15px 25px;
      cursor: pointer;
      font-weight: 500;
      transition: all 0.3s;
      border-bottom: 2px solid transparent;
      color:#001529
    }

    .tab:hover {
      background-color: var(--tab-hover-bg);
    }

    .tab.active {
      color: var(--primary-color);
      border-bottom: 2px solid var(--primary-color);
    }

    /* 页面内容 */
    .page {
      display: none;
    }

    .page.active {
      display: block;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .tab {
        padding: 12px 15px;
        font-size: 0.9rem;
      }

      .endpoint-header {
        flex-direction: column;
        align-items: flex-start;
      }

      .method {
        margin-bottom: 10px;
      }

      .auth-tag {
        margin-left: 0;
        margin-top: 10px;
      }
    }
  </style>
</head>
<body>
  <header>
    <div class="container header-content">
      <h1>家政管理系统 API 文档</h1>
      <p>本文档提供家政管理系统后端 API 的详细说明</p>
    </div>
    <div class="tabs">
      <div class="tab active" data-page="common">通用接口</div>
      <div class="tab" data-page="admin">管理后台接口</div>
      <div class="tab" data-page="app">移动端接口</div>
    </div>
  </header>

  <div class="container">
    <!-- 通用接口页面 -->
    <div id="common" class="page active">
      <section id="introduction">
        <h2>API 概述</h2>
        <p>家政管理系统 API 是一个基于 RESTful 设计的接口集合，用于管理家政服务、订单和用户。API 使用 JSON 格式进行数据交换，并使用 JWT 令牌进行身份验证。</p>

        <h3>基础 URL</h3>
        <p>所有 API 请求的基础 URL 为：<code>http://localhost:5050/api</code></p>

        <h3>API 分类</h3>
        <p>API 分为三类：</p>
        <ul>
          <li><strong>管理后台 API</strong>：<code>/api/admin/...</code> - 用于管理后台的接口</li>
          <li><strong>移动端 API</strong>：<code>/api/app/...</code> - 用于移动端应用的接口</li>
          <li><strong>通用 API</strong>：<code>/api/...</code> - 管理后台和移动端共用的接口</li>
        </ul>

        <h3>认证</h3>
        <p>大多数 API 端点需要认证。认证通过 Bearer Token 方式实现：</p>
        <pre>Authorization: Bearer &lt;your_token&gt;</pre>
        <p>获取令牌需要通过登录接口 <code>POST /api/users/login</code> 或微信登录接口获取。</p>

        <h3>权限级别</h3>
        <p>API 有以下几种权限级别：</p>
        <ul>
          <li><strong>Public</strong>：无需认证即可访问</li>
          <li><strong>Private</strong>：需要有效的认证令牌</li>
          <li><strong>Admin</strong>：需要管理员权限</li>
          <li><strong>Manager</strong>：需要管理员或经理权限</li>
          <li><strong>Customer</strong>：客户权限，适用于移动端 API</li>
        </ul>
      </section>

      <!-- 通用接口内容 -->
      <section id="common-wechat">
        <h2 class="section-toggle">微信相关 API</h2>
        <div class="section-content">
          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method post">POST</span>
              <span class="url">/api/wechat/mini-login</span>
              <span class="auth-tag">Public</span>
            </div>
            <div class="endpoint-body">
              <p>微信小程序登录</p>

              <h4>请求参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>必填</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>code</td>
                  <td>String</td>
                  <td>是</td>
                  <td>微信小程序登录时获取的临时code</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>{
  "id": 1,
  "name": "微信用户_a1b2c3d4",
  "avatar": "https://thirdwx.qlogo.cn/mmopen/...",
  "role": "customer",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "openid": "oXYZ123456789"
}</pre>
            </div>
          </div>

          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method post">POST</span>
              <span class="url">/api/wechat/update-user-info</span>
              <span class="auth-tag">Public</span>
            </div>
            <div class="endpoint-body">
              <p>更新微信用户信息</p>

              <h4>请求参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>必填</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>openid</td>
                  <td>String</td>
                  <td>是</td>
                  <td>微信用户的openid</td>
                </tr>
                <tr>
                  <td>userInfo</td>
                  <td>Object</td>
                  <td>是</td>
                  <td>微信用户信息对象</td>
                </tr>
                <tr>
                  <td>userInfo.nickName</td>
                  <td>String</td>
                  <td>是</td>
                  <td>用户昵称</td>
                </tr>
                <tr>
                  <td>userInfo.avatarUrl</td>
                  <td>String</td>
                  <td>是</td>
                  <td>用户头像URL</td>
                </tr>
                <tr>
                  <td>userInfo.gender</td>
                  <td>Number</td>
                  <td>是</td>
                  <td>用户性别(1:男, 2:女, 0:未知)</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>{
  "id": 1,
  "name": "微信昵称",
  "avatar": "https://thirdwx.qlogo.cn/mmopen/...",
  "role": "customer"
}</pre>
            </div>
          </div>

          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method post">POST</span>
              <span class="url">/api/wechat/oa-login</span>
              <span class="auth-tag">Public</span>
            </div>
            <div class="endpoint-body">
              <p>微信公众号登录</p>

              <h4>请求参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>必填</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>code</td>
                  <td>String</td>
                  <td>是</td>
                  <td>微信公众号授权后获取的临时code</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>{
  "id": 1,
  "name": "微信昵称",
  "avatar": "https://thirdwx.qlogo.cn/mmopen/...",
  "role": "customer",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "openid": "oXYZ123456789"
}</pre>
            </div>
          </div>

          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method post">POST</span>
              <span class="url">/api/wechat/bind</span>
              <span class="auth-tag">Private</span>
            </div>
            <div class="endpoint-body">
              <p>绑定微信账号</p>

              <h4>请求参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>必填</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>userId</td>
                  <td>Number</td>
                  <td>是</td>
                  <td>用户ID</td>
                </tr>
                <tr>
                  <td>openid</td>
                  <td>String</td>
                  <td>是</td>
                  <td>微信用户的openid</td>
                </tr>
                <tr>
                  <td>unionid</td>
                  <td>String</td>
                  <td>否</td>
                  <td>微信用户的unionid</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>{
  "message": "微信账号绑定成功",
  "user": {
    "id": 1,
    "name": "用户名",
    "email": "<EMAIL>",
    "role": "customer"
  }
}</pre>
            </div>
          </div>

          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method post">POST</span>
              <span class="url">/api/wechat/unbind</span>
              <span class="auth-tag">Private</span>
            </div>
            <div class="endpoint-body">
              <p>解绑微信账号</p>

              <h4>请求参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>必填</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>userId</td>
                  <td>Number</td>
                  <td>是</td>
                  <td>用户ID</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>{
  "message": "微信账号解绑成功",
  "user": {
    "id": 1,
    "name": "用户名",
    "email": "<EMAIL>",
    "role": "customer"
  }
}</pre>
            </div>
          </div>
        </div>
      </section>

      <section id="common-payment">
        <h2 class="section-toggle">支付相关 API</h2>
        <div class="section-content">
          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method post">POST</span>
              <span class="url">/api/payment/wechat/create</span>
              <span class="auth-tag">Private</span>
            </div>
            <div class="endpoint-body">
              <p>创建微信支付订单</p>

              <h4>请求参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>必填</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>orderId</td>
                  <td>Number</td>
                  <td>是</td>
                  <td>订单ID</td>
                </tr>
                <tr>
                  <td>openid</td>
                  <td>String</td>
                  <td>是</td>
                  <td>微信用户的openid（JSAPI支付必填）</td>
                </tr>
                <tr>
                  <td>tradeType</td>
                  <td>String</td>
                  <td>否</td>
                  <td>交易类型（JSAPI/NATIVE/APP，默认JSAPI）</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>{
  "message": "创建支付订单成功",
  "paymentParams": {
    "appId": "wx123456789",
    "timeStamp": "1623456789",
    "nonceStr": "abcdefghijklmnopqrstuvwxyz123456",
    "package": "prepay_id=wx123456789abcdef0",
    "signType": "MD5",
    "paySign": "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456"
  }
}</pre>
            </div>
          </div>

          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method post">POST</span>
              <span class="url">/api/payment/wechat/notify</span>
              <span class="auth-tag">Public</span>
            </div>
            <div class="endpoint-body">
              <p>微信支付通知回调</p>
              <p>注意：此接口由微信支付系统调用，不需要手动调用。</p>

              <h4>请求参数</h4>
              <p>微信支付系统以XML格式发送支付结果通知</p>

              <h4>响应示例</h4>
              <pre>&lt;xml&gt;
  &lt;return_code&gt;&lt;![CDATA[SUCCESS]]&gt;&lt;/return_code&gt;
  &lt;return_msg&gt;&lt;![CDATA[OK]]&gt;&lt;/return_msg&gt;
&lt;/xml&gt;</pre>
            </div>
          </div>

          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method get">GET</span>
              <span class="url">/api/payment/status/:orderId</span>
              <span class="auth-tag">Private</span>
            </div>
            <div class="endpoint-body">
              <p>查询支付订单状态</p>

              <h4>路径参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>orderId</td>
                  <td>Number</td>
                  <td>订单ID</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>{
  "orderId": "123",
  "paymentStatus": "completed",
  "orderStatus": "confirmed",
  "paymentMethod": "wechat",
  "amount": 100.00,
  "createdAt": "2023-06-01T12:00:00Z",
  "updatedAt": "2023-06-01T12:05:00Z"
}</pre>
            </div>
          </div>

          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method post">POST</span>
              <span class="url">/api/payment/close/:orderId</span>
              <span class="auth-tag">Private</span>
            </div>
            <div class="endpoint-body">
              <p>关闭支付订单</p>

              <h4>路径参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>orderId</td>
                  <td>Number</td>
                  <td>订单ID</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>{
  "message": "关闭支付订单成功",
  "orderId": "123"
}</pre>
            </div>
          </div>
        </div>
      </section>

      <section id="common-users">
        <h2 class="section-toggle">用户认证 API</h2>
        <div class="section-content">
          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method post">POST</span>
              <span class="url">/api/users/login</span>
              <span class="auth-tag">Public</span>
            </div>
            <div class="endpoint-body">
              <p>用户登录并获取认证令牌</p>

              <h4>请求参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>必填</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>email</td>
                  <td>String</td>
                  <td>是</td>
                  <td>用户邮箱</td>
                </tr>
                <tr>
                  <td>password</td>
                  <td>String</td>
                  <td>是</td>
                  <td>用户密码</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>{
  "id": 1,
  "name": "管理员",
  "email": "<EMAIL>",
  "role": "admin",
  "phone": "13800138000",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}</pre>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- 管理后台接口页面 -->
    <div id="admin" class="page">
      <section id="admin-introduction">
        <h2>管理后台 API 概述</h2>
        <p>管理后台 API 提供了家政管理系统后台管理功能的接口，包括用户管理、服务管理、订单管理等功能。</p>
        <p>所有管理后台 API 的基础路径为：<code>/api/admin/...</code></p>
      </section>

      <!-- 管理后台接口内容 -->
      <section id="admin-users">
        <h2 class="section-toggle">用户管理 API</h2>
        <div class="section-content">
          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method get">GET</span>
              <span class="url">/api/admin/users</span>
              <span class="auth-tag">Admin</span>
            </div>
            <div class="endpoint-body">
              <p>获取所有用户列表</p>

              <h4>响应示例</h4>
              <pre>[
  {
    "id": 1,
    "name": "管理员",
    "email": "<EMAIL>",
    "role": "admin",
    "phone": "13800138000"
  },
  {
    "id": 2,
    "name": "员工1",
    "email": "<EMAIL>",
    "role": "staff",
    "phone": "13900139000"
  }
]</pre>
            </div>
          </div>

          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method post">POST</span>
              <span class="url">/api/admin/users</span>
              <span class="auth-tag">Admin</span>
            </div>
            <div class="endpoint-body">
              <p>创建新用户</p>

              <h4>请求参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>必填</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>name</td>
                  <td>String</td>
                  <td>是</td>
                  <td>用户姓名</td>
                </tr>
                <tr>
                  <td>email</td>
                  <td>String</td>
                  <td>是</td>
                  <td>用户邮箱</td>
                </tr>
                <tr>
                  <td>password</td>
                  <td>String</td>
                  <td>是</td>
                  <td>用户密码</td>
                </tr>
                <tr>
                  <td>role</td>
                  <td>String</td>
                  <td>否</td>
                  <td>用户角色 (admin/manager/staff)</td>
                </tr>
                <tr>
                  <td>phone</td>
                  <td>String</td>
                  <td>是</td>
                  <td>用户电话</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>{
  "id": 3,
  "name": "新员工",
  "email": "<EMAIL>",
  "role": "staff",
  "phone": "13700137000"
}</pre>
            </div>
          </div>

          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method put">PUT</span>
              <span class="url">/api/admin/users/:id</span>
              <span class="auth-tag">Admin</span>
            </div>
            <div class="endpoint-body">
              <p>更新用户信息</p>

              <h4>路径参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>id</td>
                  <td>Number</td>
                  <td>用户ID</td>
                </tr>
              </table>

              <h4>请求参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>必填</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>name</td>
                  <td>String</td>
                  <td>否</td>
                  <td>用户姓名</td>
                </tr>
                <tr>
                  <td>email</td>
                  <td>String</td>
                  <td>否</td>
                  <td>用户邮箱</td>
                </tr>
                <tr>
                  <td>password</td>
                  <td>String</td>
                  <td>否</td>
                  <td>用户密码</td>
                </tr>
                <tr>
                  <td>role</td>
                  <td>String</td>
                  <td>否</td>
                  <td>用户角色 (admin/manager/staff)</td>
                </tr>
                <tr>
                  <td>phone</td>
                  <td>String</td>
                  <td>否</td>
                  <td>用户电话</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>{
  "id": 3,
  "name": "更新后的员工",
  "email": "<EMAIL>",
  "role": "manager",
  "phone": "13700137000"
}</pre>
            </div>
          </div>

          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method delete">DELETE</span>
              <span class="url">/api/admin/users/:id</span>
              <span class="auth-tag">Admin</span>
            </div>
            <div class="endpoint-body">
              <p>删除用户</p>

              <h4>路径参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>id</td>
                  <td>Number</td>
                  <td>用户ID</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>{
  "message": "用户删除成功"
}</pre>
            </div>
          </div>
        </div>
      </section>

      <section id="admin-services">
        <h2 class="section-toggle">服务管理 API</h2>
        <div class="section-content">
          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method post">POST</span>
              <span class="url">/api/admin/services</span>
              <span class="auth-tag">Admin</span>
            </div>
            <div class="endpoint-body">
              <p>创建新服务</p>

              <h4>请求参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>必填</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>name</td>
                  <td>String</td>
                  <td>是</td>
                  <td>服务名称</td>
                </tr>
                <tr>
                  <td>description</td>
                  <td>String</td>
                  <td>是</td>
                  <td>服务描述</td>
                </tr>
                <tr>
                  <td>price</td>
                  <td>Number</td>
                  <td>是</td>
                  <td>服务价格</td>
                </tr>
                <tr>
                  <td>duration</td>
                  <td>Number</td>
                  <td>是</td>
                  <td>服务时长（小时）</td>
                </tr>
                <tr>
                  <td>category</td>
                  <td>String</td>
                  <td>是</td>
                  <td>服务类别</td>
                </tr>
                <tr>
                  <td>isActive</td>
                  <td>Boolean</td>
                  <td>否</td>
                  <td>是否激活（默认true）</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>{
  "id": 3,
  "name": "厨房深度清洁",
  "description": "专业厨房深度清洁服务",
  "price": 200.00,
  "duration": 3,
  "category": "深度清洁",
  "isActive": true
}</pre>
            </div>
          </div>

          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method put">PUT</span>
              <span class="url">/api/admin/services/:id</span>
              <span class="auth-tag">Admin</span>
            </div>
            <div class="endpoint-body">
              <p>更新服务信息</p>

              <h4>路径参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>id</td>
                  <td>Number</td>
                  <td>服务ID</td>
                </tr>
              </table>

              <h4>请求参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>必填</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>name</td>
                  <td>String</td>
                  <td>否</td>
                  <td>服务名称</td>
                </tr>
                <tr>
                  <td>description</td>
                  <td>String</td>
                  <td>否</td>
                  <td>服务描述</td>
                </tr>
                <tr>
                  <td>price</td>
                  <td>Number</td>
                  <td>否</td>
                  <td>服务价格</td>
                </tr>
                <tr>
                  <td>duration</td>
                  <td>Number</td>
                  <td>否</td>
                  <td>服务时长（小时）</td>
                </tr>
                <tr>
                  <td>category</td>
                  <td>String</td>
                  <td>否</td>
                  <td>服务类别</td>
                </tr>
                <tr>
                  <td>isActive</td>
                  <td>Boolean</td>
                  <td>否</td>
                  <td>是否激活</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>{
  "id": 3,
  "name": "高级厨房深度清洁",
  "description": "专业厨房深度清洁服务，包含油烟机清洗",
  "price": 250.00,
  "duration": 4,
  "category": "深度清洁",
  "isActive": true
}</pre>
            </div>
          </div>

          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method delete">DELETE</span>
              <span class="url">/api/admin/services/:id</span>
              <span class="auth-tag">Admin</span>
            </div>
            <div class="endpoint-body">
              <p>删除服务</p>

              <h4>路径参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>id</td>
                  <td>Number</td>
                  <td>服务ID</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>{
  "message": "服务删除成功"
}</pre>
            </div>
          </div>
        </div>
      </section>

      <section id="admin-orders">
        <h2 class="section-toggle">订单管理 API</h2>
        <div class="section-content">
          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method get">GET</span>
              <span class="url">/api/admin/orders</span>
              <span class="auth-tag">Admin</span>
            </div>
            <div class="endpoint-body">
              <p>获取所有订单列表</p>

              <h4>查询参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>必填</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>status</td>
                  <td>String</td>
                  <td>否</td>
                  <td>订单状态筛选</td>
                </tr>
                <tr>
                  <td>page</td>
                  <td>Number</td>
                  <td>否</td>
                  <td>页码（默认1）</td>
                </tr>
                <tr>
                  <td>limit</td>
                  <td>Number</td>
                  <td>否</td>
                  <td>每页数量（默认10）</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>{
  "orders": [
    {
      "id": 1,
      "customerId": 5,
      "serviceId": 2,
      "status": "confirmed",
      "scheduledDate": "2023-06-15T09:00:00Z",
      "totalAmount": 300.00,
      "paymentStatus": "paid",
      "createdAt": "2023-06-10T14:30:00Z",
      "customer": {
        "name": "张三",
        "phone": "13812345678"
      },
      "service": {
        "name": "深度清洁"
      }
    }
  ],
  "pagination": {
    "total": 25,
    "page": 1,
    "limit": 10,
    "pages": 3
  }
}</pre>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- 移动端接口页面 -->
    <div id="app" class="page">
      <section id="app-introduction">
        <h2>移动端 API 概述</h2>
        <p>移动端 API 提供了家政管理系统移动应用的接口，包括服务浏览、订单管理、用户信息等功能。</p>
        <p>所有移动端 API 的基础路径为：<code>/api/app/...</code></p>
      </section>

      <!-- 移动端接口内容 -->
      <section id="app-services">
        <h2 class="section-toggle">服务浏览 API</h2>
        <div class="section-content">
          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method get">GET</span>
              <span class="url">/api/app/services</span>
              <span class="auth-tag">Public</span>
            </div>
            <div class="endpoint-body">
              <p>获取所有可用服务列表</p>

              <h4>查询参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>必填</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>category</td>
                  <td>String</td>
                  <td>否</td>
                  <td>服务类别筛选</td>
                </tr>
                <tr>
                  <td>sort</td>
                  <td>String</td>
                  <td>否</td>
                  <td>排序方式(price_asc, price_desc, popular)</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>[
  {
    "id": 1,
    "name": "日常保洁",
    "description": "提供日常家居清洁服务",
    "price": 100.00,
    "duration": 2,
    "category": "日常保洁",
    "imageUrl": "https://example.com/images/daily-cleaning.jpg",
    "rating": 4.8,
    "reviewCount": 125
  },
  {
    "id": 2,
    "name": "深度清洁",
    "description": "提供全面深度清洁服务",
    "price": 300.00,
    "duration": 4,
    "category": "深度清洁",
    "imageUrl": "https://example.com/images/deep-cleaning.jpg",
    "rating": 4.9,
    "reviewCount": 89
  }
]</pre>
            </div>
          </div>

          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method get">GET</span>
              <span class="url">/api/app/services/:id</span>
              <span class="auth-tag">Public</span>
            </div>
            <div class="endpoint-body">
              <p>获取服务详情</p>

              <h4>路径参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>id</td>
                  <td>Number</td>
                  <td>服务ID</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>{
  "id": 1,
  "name": "日常保洁",
  "description": "提供日常家居清洁服务，包括地面清洁、家具除尘、厨房清洁等",
  "price": 100.00,
  "duration": 2,
  "category": "日常保洁",
  "imageUrl": "https://example.com/images/daily-cleaning.jpg",
  "rating": 4.8,
  "reviewCount": 125,
  "features": [
    "地面清洁",
    "家具除尘",
    "厨房清洁",
    "卫生间消毒"
  ],
  "reviews": [
    {
      "id": 1,
      "rating": 5,
      "comment": "服务很专业，清洁得很干净",
      "userName": "李先生",
      "createdAt": "2023-05-20T14:30:00Z"
    },
    {
      "id": 2,
      "rating": 4,
      "comment": "整体不错，就是时间稍微长了点",
      "userName": "王女士",
      "createdAt": "2023-05-18T09:15:00Z"
    }
  ]
}</pre>
            </div>
          </div>
        </div>
      </section>

      <section id="app-orders">
        <h2 class="section-toggle">订单管理 API</h2>
        <div class="section-content">
          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method post">POST</span>
              <span class="url">/api/app/orders</span>
              <span class="auth-tag">Private</span>
            </div>
            <div class="endpoint-body">
              <p>创建新订单</p>

              <h4>请求参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>必填</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>serviceId</td>
                  <td>Number</td>
                  <td>是</td>
                  <td>服务ID</td>
                </tr>
                <tr>
                  <td>scheduledDate</td>
                  <td>String</td>
                  <td>是</td>
                  <td>预约日期时间(ISO格式)</td>
                </tr>
                <tr>
                  <td>address</td>
                  <td>String</td>
                  <td>是</td>
                  <td>服务地址</td>
                </tr>
                <tr>
                  <td>contactName</td>
                  <td>String</td>
                  <td>是</td>
                  <td>联系人姓名</td>
                </tr>
                <tr>
                  <td>contactPhone</td>
                  <td>String</td>
                  <td>是</td>
                  <td>联系人电话</td>
                </tr>
                <tr>
                  <td>remark</td>
                  <td>String</td>
                  <td>否</td>
                  <td>订单备注</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>{
  "id": 10,
  "customerId": 5,
  "serviceId": 1,
  "status": "pending",
  "scheduledDate": "2023-06-20T14:00:00Z",
  "address": "北京市朝阳区建国路88号",
  "contactName": "张三",
  "contactPhone": "13812345678",
  "totalAmount": 100.00,
  "paymentStatus": "unpaid",
  "createdAt": "2023-06-15T10:30:00Z",
  "service": {
    "name": "日常保洁",
    "duration": 2
  }
}</pre>
            </div>
          </div>

          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method get">GET</span>
              <span class="url">/api/app/orders</span>
              <span class="auth-tag">Private</span>
            </div>
            <div class="endpoint-body">
              <p>获取当前用户的订单列表</p>

              <h4>查询参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>必填</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>status</td>
                  <td>String</td>
                  <td>否</td>
                  <td>订单状态筛选</td>
                </tr>
                <tr>
                  <td>page</td>
                  <td>Number</td>
                  <td>否</td>
                  <td>页码（默认1）</td>
                </tr>
                <tr>
                  <td>limit</td>
                  <td>Number</td>
                  <td>否</td>
                  <td>每页数量（默认10）</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>{
  "orders": [
    {
      "id": 10,
      "serviceId": 1,
      "status": "pending",
      "scheduledDate": "2023-06-20T14:00:00Z",
      "totalAmount": 100.00,
      "paymentStatus": "unpaid",
      "createdAt": "2023-06-15T10:30:00Z",
      "service": {
        "name": "日常保洁",
        "imageUrl": "https://example.com/images/daily-cleaning.jpg"
      }
    }
  ],
  "pagination": {
    "total": 5,
    "page": 1,
    "limit": 10,
    "pages": 1
  }
}</pre>
            </div>
          </div>

          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method get">GET</span>
              <span class="url">/api/app/orders/:id</span>
              <span class="auth-tag">Private</span>
            </div>
            <div class="endpoint-body">
              <p>获取订单详情</p>

              <h4>路径参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>id</td>
                  <td>Number</td>
                  <td>订单ID</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>{
  "id": 10,
  "customerId": 5,
  "serviceId": 1,
  "status": "pending",
  "scheduledDate": "2023-06-20T14:00:00Z",
  "address": "北京市朝阳区建国路88号",
  "contactName": "张三",
  "contactPhone": "13812345678",
  "totalAmount": 100.00,
  "paymentStatus": "unpaid",
  "createdAt": "2023-06-15T10:30:00Z",
  "service": {
    "name": "日常保洁",
    "description": "提供日常家居清洁服务",
    "duration": 2,
    "imageUrl": "https://example.com/images/daily-cleaning.jpg"
  },
  "staff": null,
  "timeline": [
    {
      "status": "created",
      "time": "2023-06-15T10:30:00Z",
      "description": "订单创建成功"
    }
  ]
}</pre>
            </div>
          </div>

          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method put">PUT</span>
              <span class="url">/api/app/orders/:id/cancel</span>
              <span class="auth-tag">Private</span>
            </div>
            <div class="endpoint-body">
              <p>取消订单</p>

              <h4>路径参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>id</td>
                  <td>Number</td>
                  <td>订单ID</td>
                </tr>
              </table>

              <h4>请求参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>必填</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>reason</td>
                  <td>String</td>
                  <td>否</td>
                  <td>取消原因</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>{
  "id": 10,
  "status": "cancelled",
  "message": "订单取消成功"
}</pre>
            </div>
          </div>
        </div>
      </section>

      <section id="app-user">
        <h2 class="section-toggle">用户信息 API</h2>
        <div class="section-content">
          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method get">GET</span>
              <span class="url">/api/app/user/profile</span>
              <span class="auth-tag">Private</span>
            </div>
            <div class="endpoint-body">
              <p>获取当前用户个人资料</p>

              <h4>响应示例</h4>
              <pre>{
  "id": 5,
  "name": "张三",
  "avatar": "https://example.com/avatars/user5.jpg",
  "phone": "13812345678",
  "email": "<EMAIL>",
  "gender": "male",
  "address": [
    {
      "id": 1,
      "name": "家",
      "address": "北京市朝阳区建国路88号",
      "isDefault": true
    },
    {
      "id": 2,
      "name": "公司",
      "address": "北京市海淀区中关村大街1号",
      "isDefault": false
    }
  ],
  "wechatBound": true
}</pre>
            </div>
          </div>

          <div class="endpoint">
            <div class="endpoint-header">
              <span class="method put">PUT</span>
              <span class="url">/api/app/user/profile</span>
              <span class="auth-tag">Private</span>
            </div>
            <div class="endpoint-body">
              <p>更新当前用户个人资料</p>

              <h4>请求参数</h4>
              <table>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>必填</th>
                  <th>描述</th>
                </tr>
                <tr>
                  <td>name</td>
                  <td>String</td>
                  <td>否</td>
                  <td>用户姓名</td>
                </tr>
                <tr>
                  <td>phone</td>
                  <td>String</td>
                  <td>否</td>
                  <td>用户电话</td>
                </tr>
                <tr>
                  <td>email</td>
                  <td>String</td>
                  <td>否</td>
                  <td>用户邮箱</td>
                </tr>
                <tr>
                  <td>gender</td>
                  <td>String</td>
                  <td>否</td>
                  <td>用户性别(male/female/other)</td>
                </tr>
              </table>

              <h4>响应示例</h4>
              <pre>{
  "id": 5,
  "name": "张三",
  "avatar": "https://example.com/avatars/user5.jpg",
  "phone": "13812345678",
  "email": "<EMAIL>",
  "gender": "male",
  "message": "个人资料更新成功"
}</pre>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>

  <script>
    // 切换标签页
    document.querySelectorAll('.tab').forEach(tab => {
      tab.addEventListener('click', () => {
        // 移除所有标签和页面的活动状态
        document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
        document.querySelectorAll('.page').forEach(p => p.classList.remove('active'));

        // 添加当前标签和页面的活动状态
        tab.classList.add('active');
        const pageId = tab.getAttribute('data-page');
        document.getElementById(pageId).classList.add('active');
      });
    });

    // 折叠/展开端点
    document.addEventListener('DOMContentLoaded', () => {
      document.querySelectorAll('.endpoint-header').forEach(header => {
        header.addEventListener('click', () => {
          const endpoint = header.parentElement;
          endpoint.classList.toggle('active');
        });
      });

      // 折叠/展开部分
      document.querySelectorAll('.section-toggle').forEach(toggle => {
        toggle.addEventListener('click', () => {
          toggle.classList.toggle('collapsed');
          const content = toggle.nextElementSibling;
          content.classList.toggle('collapsed');
        });
      });
    });
  </script>
</body>
</html>
