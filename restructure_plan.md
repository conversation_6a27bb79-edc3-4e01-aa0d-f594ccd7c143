# 后端项目重构计划

## 新的目录结构

```
backend/
├── config/                  # 配置文件
│   ├── database.js          # 数据库配置
│   ├── wechat.js            # 微信相关配置
│   └── payment.js           # 支付相关配置
├── controllers/             # 控制器
│   ├── admin/               # 管理后台控制器
│   │   ├── userController.js
│   │   ├── serviceController.js
│   │   ├── orderController.js
│   │   ├── customerController.js
│   │   ├── staffController.js
│   │   ├── financeController.js
│   │   ├── reviewController.js
│   │   ├── scheduleController.js
│   │   └── statsController.js
│   ├── app/                 # 移动端 App 控制器
│   │   ├── userController.js
│   │   ├── serviceController.js
│   │   ├── orderController.js
│   │   ├── reviewController.js
│   │   └── profileController.js
│   └── common/              # 通用控制器
│       ├── authController.js
│       ├── wechatController.js
│       └── paymentController.js
├── middleware/              # 中间件
│   ├── authMiddleware.js    # 认证中间件
│   ├── errorMiddleware.js   # 错误处理中间件
│   ├── validationMiddleware.js # 数据验证中间件
│   └── roleMiddleware.js    # 角色权限中间件
├── models/                  # 数据模型
│   ├── index.js             # 模型入口文件
│   ├── userModel.js
│   ├── serviceModel.js
│   ├── orderModel.js
│   ├── customerModel.js
│   ├── reviewModel.js
│   ├── scheduleModel.js
│   ├── financeModel.js
│   └── paymentModel.js      # 新增支付模型
├── routes/                  # 路由
│   ├── admin/               # 管理后台路由
│   │   ├── index.js         # 管理后台路由入口
│   │   ├── userRoutes.js
│   │   ├── serviceRoutes.js
│   │   ├── orderRoutes.js
│   │   ├── customerRoutes.js
│   │   ├── staffRoutes.js
│   │   ├── financeRoutes.js
│   │   ├── reviewRoutes.js
│   │   ├── scheduleRoutes.js
│   │   └── statsRoutes.js
│   ├── app/                 # 移动端 App 路由
│   │   ├── index.js         # 移动端路由入口
│   │   ├── userRoutes.js
│   │   ├── serviceRoutes.js
│   │   ├── orderRoutes.js
│   │   ├── reviewRoutes.js
│   │   └── profileRoutes.js
│   └── common/              # 通用路由
│       ├── index.js         # 通用路由入口
│       ├── authRoutes.js
│       ├── wechatRoutes.js
│       └── paymentRoutes.js
├── services/                # 服务层
│   ├── authService.js       # 认证服务
│   ├── wechatService.js     # 微信服务
│   ├── paymentService.js    # 支付服务
│   ├── emailService.js      # 邮件服务
│   └── smsService.js        # 短信服务
├── utils/                   # 工具函数
│   ├── apiResponse.js       # API 响应格式化
│   ├── logger.js            # 日志工具
│   ├── validator.js         # 数据验证工具
│   ├── encryption.js        # 加密工具
│   └── wechatUtil.js        # 微信工具函数
├── docs/                    # API 文档
│   └── swagger.json         # Swagger 文档
├── tests/                   # 测试文件
├── .env                     # 环境变量
├── .gitignore               # Git 忽略文件
├── index.js                 # 应用入口文件
├── app.js                   # Express 应用配置
└── package.json             # 项目依赖
```

## 重构步骤

1. 创建新的目录结构
2. 将现有控制器分离为管理后台和 App 控制器
3. 创建通用控制器和服务
4. 实现微信登录和支付功能
5. 更新路由配置
6. 更新入口文件
7. 添加新的依赖包
8. 测试 API 接口

## 新增功能

### 微信登录

- 实现微信授权登录
- 获取用户信息
- 关联现有账户

### 微信支付

- 统一下单接口
- 支付结果通知
- 订单查询
- 退款接口

### 其他功能

- 短信验证码登录
- 推送通知
- 文件上传
