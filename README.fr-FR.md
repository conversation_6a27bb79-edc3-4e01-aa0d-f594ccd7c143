Language : [🇺🇸](./README.md) | [🇨🇳](./README.zh-CN.md) | [🇷🇺](./README.ru-RU.md) | [🇹🇷](./README.tr-TR.md) | [🇯🇵](./README.ja-JP.md) | 🇫🇷 | [🇵🇹](./README.pt-BR.md) | [🇸🇦](./README.ar-DZ.md)

<h1 align="center">Ant Design Pro</h1>

<div align="center">

Une solution UI prête à l'emploi pour des applications d'entreprise en tant que modèle React.

[![Node CI](https://github.com/ant-design/ant-design-pro/actions/workflows/ci.yml/badge.svg)](https://github.com/ant-design/ant-design-pro/actions/workflows/ci.yml) [![Preview Deploy](https://github.com/ant-design/ant-design-pro/actions/workflows/preview-deploy.yml/badge.svg)](https://github.com/ant-design/ant-design-pro/actions/workflows/preview-deploy.yml) [![Build With Umi](https://img.shields.io/badge/build%20with-umi-028fe4.svg?style=flat-square)](http://umijs.org/) ![](https://badgen.net/badge/icon/Ant%20Design?icon=https://gw.alipayobjects.com/zos/antfincdn/Pp4WPgVDB3/KDpgvguMpGfqaHPjicRK.svg&label)

![](https://github.com/user-attachments/assets/fde29061-3d9a-4397-8ac2-397b0e033ef5)

</div>

- Aperçu: http://preview.pro.ant.design
- Page d'accueil: http://pro.ant.design
- Documentation: http://pro.ant.design/docs/getting-started
- ChangeLog: http://pro.ant.design/docs/changelog
- FAQ: http://pro.ant.design/docs/faq

## 2.0 Sorti maintenant! 🎉🎉🎉

[Annoncement de Ant Design Pro 2.0.0](https://medium.com/ant-design/beautiful-and-powerful-ant-design-pro-2-0-release-51358da5af95)

## Recrutement pour la traduction :loudspeaker:

Nous avons besoin de votre aide: https://github.com/ant-design/ant-design-pro/issues/120

## Fonctionnalités

- :gem: **Design soigné**: Suit [la spécification Ant Design](http://ant.design/)
- :triangular_ruler: **Modèles communs**: Modèles typiques d'application d'entreprise
- :rocket: **Développement dernier cri**: Infrastructure de développement de React/umi/dva/antd la plus récente
- :iphone: **Design adapté**: Conçu pour des tailles d'écran variables
- :art: **Thématisation**: Thème personnalisable avec configuration simple
- :globe_with_meridians: **International**: Solution i18n intégrée
- :gear: **Meilleures pratiques**: Flux de travail solide pour rendre votre code sain
- :1234: **Développement simulé**: Solution de développement simulée facile à utiliser
- :white_check_mark: **Tests UI**: Volez en toute sécurité avec les tests unitaires et e2e

## Modèles

```
- Tableau de bord
  - Analytique
  - Moniteur
  - Espace de travail
- Formulaire
  - Formulaire de base
  - Formulaire par étape
  - Formulaire avancé
- Liste
  - Tableau standard
  - Liste standard
  - Liste de cartes
  - Liste de recherche (Projet/Applications/Article)
- Profil
  - Profil simple
  - Profil avancé
- Compte
  - Centre du compte
  - Paramètres du compte
- Résultat
  - Succès
  - Échec
- Exception
  - 403
  - 404
  - 500
- Utilisateur
  - Connexion
  - S'inscrire
  - Résultat de l'inscription
```

## Utilisation

### Utiliser bash

```bash
$ mkdir <your-project-name>
$ cd <your-project-name>
$ yarn create umi  # or npm create umi

# Choose ant-design-pro:
 Select the boilerplate type (Use arrow keys)
❯ ant-design-pro  - Create project with an layout-only ant-design-pro boilerplate, use together with umi block.
  app             - Create project with a simple boilerplate, support typescript.
  block           - Create a umi block.
  library         - Create a library with umi.
  plugin          - Create a umi plugin.

$ git init
$ npm install
$ npm start         # visit http://localhost:8000
```

## Support des navigateurs

Navigateurs modernes.

| [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt="Edge" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Edge | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/firefox/firefox_48x48.png" alt="Firefox" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Firefox | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/chrome/chrome_48x48.png" alt="Chrome" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Chrome | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/safari/safari_48x48.png" alt="Safari" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Safari | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/opera/opera_48x48.png" alt="Opera" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Opera |
| --- | --- | --- | --- | --- |
| Edge | deux dernières versions | deux dernières versions | deux dernières versions | deux dernières versions |

## Contribution

Toute forme de contribution est la bienvenue, voici quelques exemples de façons dont vous pouvez contribuer à ce projet:

- Utiliser Ant Design Pro dans votre travail quotidien.
- Soumettre des [issues](http://github.com/ant-design/ant-design-pro/issues) pour reporter les bugs ou poser des questions.
- Proposer des [pull requests](http://github.com/ant-design/ant-design-pro/pulls) pour améliorer notre code.
